version: '3.8'

services:
  fake-gcs-server:
    # Utilise une image multi-architecture qui fonctionne sur macOS (M1/M2) et Linux
    image: fsouza/fake-gcs-server:1.47.6
    platform: linux/amd64  # Force x86_64 pour éviter les problèmes ARM64 sur Mac M1/M2
    ports:
      - "4443:4443"
    command: [
      "-scheme", "http",
      "-host", "0.0.0.0",
      "-port", "4443",
      "-public-host", "localhost:4443",
      "-backend", "memory"  # Utilise le backend mémoire pour les tests
    ]
    volumes:
      - ./test-data:/data
    environment:
      - STORAGE_EMULATOR_HOST=localhost:4443
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:4443/storage/v1/b"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Alternative pour macOS avec ARM64 natif si besoin
  fake-gcs-server-arm:
    image: fsouza/fake-gcs-server:latest
    platform: linux/arm64
    ports:
      - "4444:4443"
    command: [
      "-scheme", "http",
      "-host", "0.0.0.0",
      "-port", "4443",
      "-public-host", "localhost:4444",
      "-backend", "memory"
    ]
    profiles:
      - arm64  # Utilise ce profil avec: docker-compose --profile arm64 up
    environment:
      - STORAGE_EMULATOR_HOST=localhost:4444

  test-runner:
    build: .
    depends_on:
      fake-gcs-server:
        condition: service_healthy
    environment:
      - STORAGE_EMULATOR_HOST=http://localhost:4443
      - GOOGLE_CLOUD_PROJECT=test-project
      - PYTHONPATH=/app/src
    volumes:
      - .:/app
    working_dir: /app
    networks:
      - test-network

networks:
  test-network:
    driver: bridge
