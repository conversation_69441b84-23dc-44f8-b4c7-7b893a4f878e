# Exclude documentation and test directories
src/kbotloadscheduler/loader/confluence/docs
src/kbotloadscheduler/loader/confluence/tests
src/kbotloadscheduler/loader/confluence/reports
src/kbotloadscheduler/loader/confluence/examples

# Common exclusions for Python projects
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
.Python
*.so
.pytest_cache
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Development and IDE files
.github/
.git
.gitignore
.vscode/
.idea/
*.swp
*.swo
*~

# Documentation (if you have other docs you want to exclude)
*.md
docs/
doc/

# Other test directories (adjust as needed)
**/tests/
test/
testing/
tests/

# Environment and config files
.env
.env.local
.env.*.local
.env.example
docker-compose*.yml
Dockerfile*
.dockerignore
