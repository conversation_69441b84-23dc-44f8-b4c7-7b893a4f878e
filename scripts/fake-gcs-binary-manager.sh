#!/bin/bash

# Script d'installation et de gestion de fake-gcs-server en binaire
# Compatible macOS (Intel/M1/M2) et Linux (x86_64/ARM64)

set -e

# Configuration
FAKE_GCS_VERSION="v1.52.2"  # Version la plus récente
INSTALL_DIR="$HOME/.local/bin"
BINARY_NAME="fake-gcs-server"
BINARY_PATH="$INSTALL_DIR/$BINARY_NAME"
DATA_DIR="$HOME/.fake-gcs-data"
CONFIG_DIR="$HOME/.fake-gcs-config"
PID_FILE="/tmp/fake-gcs-server.pid"
LOG_FILE="/tmp/fake-gcs-server.log"

# Détection de la plateforme
detect_platform() {
    local os=$(uname -s | tr '[:upper:]' '[:lower:]')
    local arch=$(uname -m)

    case "$arch" in
        "x86_64"|"amd64")
            arch="amd64"
            ;;
        "arm64"|"aarch64")
            arch="arm64"
            ;;
        *)
            echo "❌ Architecture non supportée: $arch"
            exit 1
            ;;
    esac

    case "$os" in
        "darwin")
            os="darwin"
            ;;
        "linux")
            os="linux"
            ;;
        *)
            echo "❌ OS non supporté: $os"
            exit 1
            ;;
    esac

    echo "${os}_${arch}"
}

# Installation du binaire
install_fake_gcs_server() {
    local platform=$(detect_platform)
    local download_url="https://github.com/fsouza/fake-gcs-server/releases/download/${FAKE_GCS_VERSION}/fake-gcs-server_${platform}.tar.gz"

    echo "🔍 Plateforme détectée: $platform"
    echo "📦 Installation de fake-gcs-server $FAKE_GCS_VERSION..."

    # Création du répertoire d'installation
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$CONFIG_DIR"

    # Vérification si déjà installé
    if [[ -f "$BINARY_PATH" ]]; then
        local current_version=$("$BINARY_PATH" -version 2>/dev/null | head -n1 || echo "inconnue")
        echo "ℹ️  Version installée: $current_version"
        read -p "Voulez-vous réinstaller ? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "✅ Installation annulée"
            return 0
        fi
    fi

    # Téléchargement et installation
    local temp_dir=$(mktemp -d)
    local archive_file="$temp_dir/fake-gcs-server.tar.gz"

    echo "⬇️  Téléchargement depuis: $download_url"

    if command -v curl >/dev/null 2>&1; then
        curl -L -o "$archive_file" "$download_url"
    elif command -v wget >/dev/null 2>&1; then
        wget -O "$archive_file" "$download_url"
    else
        echo "❌ curl ou wget requis pour le téléchargement"
        exit 1
    fi

    # Extraction
    echo "📂 Extraction..."
    tar -xzf "$archive_file" -C "$temp_dir"

    # Installation
    if [[ -f "$temp_dir/fake-gcs-server" ]]; then
        cp "$temp_dir/fake-gcs-server" "$BINARY_PATH"
        chmod +x "$BINARY_PATH"
        echo "✅ fake-gcs-server installé dans $BINARY_PATH"
    else
        echo "❌ Binaire non trouvé après extraction"
        ls -la "$temp_dir"
        exit 1
    fi

    # Nettoyage
    rm -rf "$temp_dir"

    # Vérification de l'installation
    if "$BINARY_PATH" -version >/dev/null 2>&1; then
        echo "✅ Installation réussie!"
        "$BINARY_PATH" -version | head -n1
    else
        echo "❌ Erreur lors de la vérification de l'installation"
        exit 1
    fi

    # Ajout au PATH si nécessaire
    if ! echo "$PATH" | grep -q "$INSTALL_DIR"; then
        echo ""
        echo "💡 Pour utiliser fake-gcs-server depuis n'importe où, ajoutez à votre shell:"
        echo "   export PATH=\"\$PATH:$INSTALL_DIR\""
        echo ""
        echo "   Ou ajoutez cette ligne à votre ~/.bashrc, ~/.zshrc, etc."
    fi
}

# Démarrage du serveur
start_server() {
    local port=${1:-4443}
    local scheme=${2:-http}
    local backend=${3:-memory}

    if is_running; then
        echo "⚠️  fake-gcs-server est déjà en cours d'exécution (PID: $(cat $PID_FILE))"
        return 1
    fi

    echo "🚀 Démarrage de fake-gcs-server..."
    echo "   Port: $port"
    echo "   Schéma: $scheme"
    echo "   Backend: $backend"
    echo "   Données: $DATA_DIR"
    echo "   Logs: $LOG_FILE"

    # Commande de démarrage
    nohup "$BINARY_PATH" \
        -scheme "$scheme" \
        -host "0.0.0.0" \
        -port "$port" \
        -public-host "localhost:$port" \
        -backend "$backend" \
        -data "$DATA_DIR" \
        > "$LOG_FILE" 2>&1 &

    local pid=$!
    echo $pid > "$PID_FILE"

    # Attente de la disponibilité
    echo "⏳ Attente de la disponibilité du serveur..."
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$port/storage/v1/b" >/dev/null 2>&1; then
            echo "✅ fake-gcs-server démarré avec succès!"
            echo "   URL: http://localhost:$port"
            echo "   PID: $pid"

            # Configuration des variables d'environnement
            echo ""
            echo "🔧 Variables d'environnement à configurer:"
            echo "   export STORAGE_EMULATOR_HOST=localhost:$port"
            echo "   export GOOGLE_CLOUD_PROJECT=test-project"

            return 0
        fi

        if ! kill -0 $pid 2>/dev/null; then
            echo "❌ Le processus fake-gcs-server s'est arrêté"
            echo "Vérifiez les logs: $LOG_FILE"
            cat "$LOG_FILE"
            rm -f "$PID_FILE"
            return 1
        fi

        echo "   Tentative $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done

    echo "❌ Timeout: fake-gcs-server n'a pas démarré dans les temps"
    stop_server
    return 1
}

# Arrêt du serveur
stop_server() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "🛑 Arrêt de fake-gcs-server (PID: $pid)..."
            kill "$pid"

            # Attente de l'arrêt
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done

            if kill -0 "$pid" 2>/dev/null; then
                echo "⚠️  Arrêt forcé..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$PID_FILE"
        echo "✅ fake-gcs-server arrêté"
    else
        echo "ℹ️  fake-gcs-server n'est pas en cours d'exécution"
    fi
}

# Vérification si le serveur est en cours d'exécution
is_running() {
    [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null
}

# Statut du serveur
status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "✅ fake-gcs-server est en cours d'exécution (PID: $pid)"

        # Test de connectivité
        local port=$(ps -p "$pid" -o args= | grep -o '\-port [0-9]*' | cut -d' ' -f2 || echo "4443")
        if curl -s -f "http://localhost:$port/storage/v1/b" >/dev/null 2>&1; then
            echo "   URL accessible: http://localhost:$port"
        else
            echo "   ⚠️  URL non accessible: http://localhost:$port"
        fi
    else
        echo "❌ fake-gcs-server n'est pas en cours d'exécution"
        if [[ -f "$PID_FILE" ]]; then
            echo "   (fichier PID orphelin supprimé)"
            rm -f "$PID_FILE"
        fi
    fi
}

# Affichage des logs
show_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        echo "📋 Logs de fake-gcs-server:"
        echo "----------------------------------------"
        tail -f "$LOG_FILE"
    else
        echo "❌ Fichier de log non trouvé: $LOG_FILE"
    fi
}

# Nettoyage des données
clean_data() {
    if is_running; then
        echo "⚠️  Arrêtez d'abord fake-gcs-server avant de nettoyer les données"
        return 1
    fi

    read -p "Êtes-vous sûr de vouloir supprimer toutes les données ? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$DATA_DIR"/*
        echo "✅ Données nettoyées"
    else
        echo "❌ Nettoyage annulé"
    fi
}

# Configuration pour les tests
setup_test_environment() {
    local port=${1:-4443}

    echo "🧪 Configuration de l'environnement de test..."

    # Démarrage du serveur si nécessaire
    if ! is_running; then
        start_server "$port" "http" "memory"
    fi

    # Configuration des variables d'environnement
    export STORAGE_EMULATOR_HOST="localhost:$port"
    export GOOGLE_CLOUD_PROJECT="test-project"

    echo "✅ Environnement de test configuré"
    echo "   STORAGE_EMULATOR_HOST=$STORAGE_EMULATOR_HOST"
    echo "   GOOGLE_CLOUD_PROJECT=$GOOGLE_CLOUD_PROJECT"

    # Création de données de test pour Confluence
    create_test_data
}

# Création de données de test
create_test_data() {
    echo "📝 Création de données de test Confluence..."

    # Script Python pour créer des données de test
    python3 -c "
import os
import json
from google.cloud import storage

# Configuration du client pour fake-gcs-server
os.environ['STORAGE_EMULATOR_HOST'] = '${STORAGE_EMULATOR_HOST:-localhost:4443}'
client = storage.Client(project='test-project')

# Création des buckets de test
buckets = ['confluence-exports-vodcastv', 'confluence-exports-legacy']
for bucket_name in buckets:
    try:
        bucket = client.create_bucket(bucket_name)
        print(f'✅ Bucket créé: {bucket_name}')
    except Exception as e:
        bucket = client.get_bucket(bucket_name)
        print(f'ℹ️  Bucket existant: {bucket_name}')

    # Création de fichiers de test
    test_files = {
        'confluence/pages/home.md': '# Accueil\\n\\nContenu de la page d\\'accueil',
        'confluence/pages/guide.html': '<h1>Guide</h1><p>Contenu du guide</p>',
        'confluence/attachments/doc.pdf': 'Mock PDF content',
        'confluence/metadata/export.json': json.dumps({'space': 'TEST', 'pages': 2})
    }

    for file_path, content in test_files.items():
        blob = bucket.blob(file_path)
        blob.upload_from_string(content)
        print(f'📁 Fichier créé: {file_path}')

print('✅ Données de test créées')
" 2>/dev/null || echo "⚠️  Erreur lors de la création des données de test (ignorée)"
}

# Aide
show_help() {
    cat << EOF
fake-gcs-server Manager - Gestionnaire de binaire fake-gcs-server

USAGE:
    $0 <command> [options]

COMMANDS:
    install                 Installe fake-gcs-server
    start [port] [scheme]   Démarre le serveur (défaut: port=4443, scheme=http)
    stop                    Arrête le serveur
    restart                 Redémarre le serveur
    status                  Affiche le statut du serveur
    logs                    Affiche les logs en temps réel
    clean                   Nettoie les données stockées
    test [port]             Configure l'environnement de test
    uninstall              Désinstalle complètement fake-gcs-server

EXEMPLES:
    $0 install                    # Installe fake-gcs-server
    $0 start                      # Démarre sur le port 4443 en HTTP
    $0 start 8080 https          # Démarre sur le port 8080 en HTTPS
    $0 test                      # Configure l'environnement de test
    $0 status                    # Vérifie si le serveur fonctionne

FICHIERS:
    Binaire:      $BINARY_PATH
    Données:      $DATA_DIR
    Config:       $CONFIG_DIR
    PID:          $PID_FILE
    Logs:         $LOG_FILE

Pour les tests Python, utilisez:
    export STORAGE_EMULATOR_HOST=localhost:4443
    export GOOGLE_CLOUD_PROJECT=test-project
EOF
}

# Désinstallation
uninstall() {
    echo "🗑️  Désinstallation de fake-gcs-server..."

    # Arrêt du serveur
    stop_server

    # Suppression des fichiers
    read -p "Supprimer le binaire ? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f "$BINARY_PATH"
        echo "✅ Binaire supprimé"
    fi

    read -p "Supprimer les données ? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$DATA_DIR" "$CONFIG_DIR"
        echo "✅ Données supprimées"
    fi

    # Nettoyage des fichiers temporaires
    rm -f "$PID_FILE" "$LOG_FILE"

    echo "✅ Désinstallation terminée"
}

# Point d'entrée principal
main() {
    case "${1:-help}" in
        "install")
            install_fake_gcs_server
            ;;
        "start")
            start_server "${2:-4443}" "${3:-http}" "${4:-memory}"
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            stop_server
            sleep 2
            start_server "${2:-4443}" "${3:-http}" "${4:-memory}"
            ;;
        "status")
            status
            ;;
        "logs")
            show_logs
            ;;
        "clean")
            clean_data
            ;;
        "test")
            setup_test_environment "${2:-4443}"
            ;;
        "uninstall")
            uninstall
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo "❌ Commande inconnue: $1"
            echo "Utilisez '$0 help' pour voir l'aide"
            exit 1
            ;;
    esac
}

# Exécution
main "$@"
