#!/bin/bash

# Script de lancement multi-plateforme pour fake-gcs-server et tests
# Compatible macOS (Intel/M1/M2) et Linux

set -e

# Détection de l'architecture
ARCH=$(uname -m)
OS=$(uname -s)

echo "🔍 Détection de la plateforme: $OS sur $ARCH"

# Configuration par défaut
COMPOSE_FILE="docker-compose.test.yml"
GCS_PORT=4443
PROFILE=""

# Adaptation selon la plateforme
case "$OS-$ARCH" in
    "Darwin-arm64")
        echo "🍎 Détection: macOS sur Apple Silicon (M1/M2)"
        echo "   Utilisation de l'émulation x86_64 via Rosetta pour la stabilité"
        # Garde la configuration par défaut (platform: linux/amd64)
        ;;
    "Darwin-x86_64")
        echo "🍎 Détection: macOS sur Intel"
        echo "   Utilisation de l'architecture native x86_64"
        ;;
    "Linux-x86_64")
        echo "🐧 Détection: Linux sur x86_64"
        echo "   Utilisation de l'architecture native"
        ;;
    "Linux-aarch64"|"Linux-arm64")
        echo "🐧 Détection: Linux sur ARM64"
        echo "   Utilisation du profil ARM64 natif"
        PROFILE="--profile arm64"
        GCS_PORT=4444
        ;;
    *)
        echo "⚠️  Plateforme non testée: $OS-$ARCH"
        echo "   Tentative avec la configuration par défaut..."
        ;;
esac

# Fonction pour nettoyer en cas d'interruption
cleanup() {
    echo "🧹 Nettoyage en cours..."
    docker-compose -f $COMPOSE_FILE $PROFILE down --volumes --remove-orphans 2>/dev/null || true
}

# Gestion des signaux
trap cleanup EXIT INT TERM

# Fonction pour vérifier si Docker est disponible
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker n'est pas installé ou pas dans le PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        echo "❌ Docker n'est pas démarré ou accessible"
        echo "   Sur macOS: Démarrez Docker Desktop"
        echo "   Sur Linux: sudo systemctl start docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose n'est pas installé"
        exit 1
    fi
}

# Fonction pour attendre que le service soit prêt
wait_for_gcs() {
    echo "⏳ Attente de fake-gcs-server sur le port $GCS_PORT..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$GCS_PORT/storage/v1/b" > /dev/null 2>&1; then
            echo "✅ fake-gcs-server est prêt!"
            return 0
        fi

        echo "   Tentative $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done

    echo "❌ Timeout: fake-gcs-server n'a pas démarré dans les temps"
    echo "   Vérification des logs:"
    docker-compose -f $COMPOSE_FILE $PROFILE logs fake-gcs-server || true
    return 1
}

# Fonction pour exécuter les tests
run_tests() {
    echo "🧪 Lancement des tests..."

    # Mise à jour de la variable d'environnement pour le bon port
    export STORAGE_EMULATOR_HOST="localhost:$GCS_PORT"

    # Tests avec pytest
    if [ -f "pytest.ini" ]; then
        echo "   Exécution avec pytest..."
        docker-compose -f $COMPOSE_FILE $PROFILE exec -T test-runner \
            python -m pytest tests/integration/test_gcs_loader_with_fake_gcs.py -v
    else
        echo "   Exécution manuelle des tests..."
        docker-compose -f $COMPOSE_FILE $PROFILE exec -T test-runner \
            python -c "
import sys
sys.path.append('/app/src')
from tests.integration.test_gcs_loader_with_fake_gcs import *
print('Tests manuels à implémenter')
"
    fi
}

# Fonction principale
main() {
    local command=${1:-"test"}

    case $command in
        "start")
            echo "🚀 Démarrage de fake-gcs-server..."
            check_docker
            docker-compose -f $COMPOSE_FILE $PROFILE up -d fake-gcs-server
            wait_for_gcs
            echo "✅ fake-gcs-server démarré sur http://localhost:$GCS_PORT"
            echo "   Pour arrêter: ./run-tests.sh stop"
            ;;

        "stop")
            echo "🛑 Arrêt des services..."
            docker-compose -f $COMPOSE_FILE $PROFILE down --volumes
            echo "✅ Services arrêtés"
            ;;

        "test")
            echo "🧪 Démarrage complet avec tests..."
            check_docker

            # Nettoyage préalable
            docker-compose -f $COMPOSE_FILE $PROFILE down --volumes --remove-orphans 2>/dev/null || true

            # Démarrage des services
            docker-compose -f $COMPOSE_FILE $PROFILE up -d

            # Attente de la disponibilité
            wait_for_gcs

            # Exécution des tests
            run_tests

            echo "✅ Tests terminés"
            ;;

        "logs")
            echo "📋 Affichage des logs..."
            docker-compose -f $COMPOSE_FILE $PROFILE logs -f
            ;;

        "shell")
            echo "🐚 Ouverture d'un shell dans le conteneur de test..."
            docker-compose -f $COMPOSE_FILE $PROFILE exec test-runner /bin/bash
            ;;

        *)
            echo "Usage: $0 {start|stop|test|logs|shell}"
            echo ""
            echo "Commandes:"
            echo "  start  - Démarre fake-gcs-server seulement"
            echo "  stop   - Arrête tous les services"
            echo "  test   - Démarre tout et lance les tests (défaut)"
            echo "  logs   - Affiche les logs en temps réel"
            echo "  shell  - Ouvre un shell dans le conteneur de test"
            echo ""
            echo "Variables d'environnement utiles:"
            echo "  STORAGE_EMULATOR_HOST=localhost:$GCS_PORT"
            echo "  GOOGLE_CLOUD_PROJECT=test-project"
            exit 1
            ;;
    esac
}

# Point d'entrée
main "$@"
