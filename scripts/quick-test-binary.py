#!/usr/bin/env python3
"""
Test rapide de fake-gcs-server avec le binaire
"""
import os
import sys
import tempfile
from pathlib import Path

# Ajout du chemin source
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_fake_gcs_binary():
    """Test rapide du binaire fake-gcs-server"""
    print("🧪 Test rapide de fake-gcs-server avec binaire")

    # Import des modules
    try:
        from google.cloud import storage
        print("✅ google-cloud-storage importé")
    except ImportError:
        print("❌ google-cloud-storage non installé")
        print("   Installation: pip install google-cloud-storage")
        return False

    try:
        from kbotloadscheduler.loader.gcs.gcs_loader import GcsLoader
        from kbotloadscheduler.bean.beans import SourceBean
        print("✅ Modules du projet importés")
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False

    # Configuration de l'environnement
    os.environ["STORAGE_EMULATOR_HOST"] = "localhost:4443"
    os.environ["GOOGLE_CLOUD_PROJECT"] = "test-project"

    # Test de connectivité
    try:
        import requests
        response = requests.get("http://localhost:4443/storage/v1/b", timeout=5)
        if response.status_code == 200:
            print("✅ fake-gcs-server accessible")
        else:
            print(f"⚠️  fake-gcs-server répond avec le code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ fake-gcs-server non accessible: {e}")
        print("   Démarrez avec: ./scripts/fake-gcs-binary-manager.sh start")
        return False

    # Test du client GCS
    try:
        client = storage.Client(project="test-project")

        # Création d'un bucket de test
        bucket_name = "test-bucket-quick"
        try:
            bucket = client.create_bucket(bucket_name)
            print(f"✅ Bucket créé: {bucket_name}")
        except Exception:
            bucket = client.get_bucket(bucket_name)
            print(f"ℹ️  Bucket existant: {bucket_name}")

        # Création de fichiers de test
        test_files = {
            "confluence/pages/test.md": "# Test Page\n\nContenu de test",
            "confluence/attachments/doc.pdf": "Mock PDF content",
            "confluence/metadata/info.json": '{"space": "TEST", "pages": 1}'
        }

        for file_path, content in test_files.items():
            blob = bucket.blob(file_path)
            blob.upload_from_string(content)
            print(f"📁 Fichier créé: {file_path}")

    except Exception as e:
        print(f"❌ Erreur avec le client GCS: {e}")
        return False

    # Test du GcsLoader
    try:
        source = SourceBean(
            code="test-source",
            domain_code="TestDomain",
            conf={
                'bucket': bucket_name,
                'prefix': 'confluence/'
            }
        )

        loader = GcsLoader()
        documents = loader.get_document_list(source)

        print(f"✅ GcsLoader a trouvé {len(documents)} documents")

        if len(documents) > 0:
            # Test de téléchargement du premier document
            with tempfile.TemporaryDirectory() as temp_dir:
                document = documents[0]
                metadata = loader.get_document(source, document, temp_dir)

                if metadata and Path(metadata['location']).exists():
                    print(f"✅ Document téléchargé: {document.name}")
                else:
                    print(f"⚠️  Problème lors du téléchargement de {document.name}")

    except Exception as e:
        print(f"❌ Erreur avec GcsLoader: {e}")
        return False

    print("🎉 Test rapide réussi !")
    print(f"   - Serveur: http://localhost:4443")
    print(f"   - Bucket: {bucket_name}")
    print(f"   - Documents: {len(documents)}")

    return True


def main():
    """Point d'entrée principal"""
    print("=" * 60)
    print("Test rapide de fake-gcs-server avec binaire")
    print("=" * 60)

    success = test_fake_gcs_binary()

    if success:
        print("\n✅ Tous les tests sont passés !")
        print("\n💡 Pour lancer les tests complets:")
        print("   pytest tests/integration/test_gcs_loader_binary.py -v")
    else:
        print("\n❌ Certains tests ont échoué")
        print("\n🔧 Vérifications:")
        print("   1. Démarrez fake-gcs-server: ./scripts/fake-gcs-binary-manager.sh start")
        print("   2. Vérifiez les dépendances: pip install google-cloud-storage")
        print("   3. Vérifiez les variables: echo $STORAGE_EMULATOR_HOST")
        sys.exit(1)


if __name__ == "__main__":
    main()
