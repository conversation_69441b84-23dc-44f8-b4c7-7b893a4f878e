"""
Configuration pour les tests avec fake-gcs-server et gcsfs
"""
import os
import tempfile
from pathlib import Path
from typing import Optional
from google.cloud import storage
import gcsfs
import pytest


class FakeGcsTestConfig:
    """Configuration pour les tests avec fake-gcs-server"""

    def __init__(self, host: str = "localhost:4443", use_ssl: bool = False):
        self.host = host
        self.use_ssl = use_ssl
        self.scheme = "https" if use_ssl else "http"
        self.endpoint_url = f"{self.scheme}://{host}"

    def setup_environment(self):
        """Configure les variables d'environnement pour fake-gcs-server"""
        os.environ["STORAGE_EMULATOR_HOST"] = self.host
        os.environ["GOOGLE_CLOUD_PROJECT"] = "test-project"

    def create_storage_client(self) -> storage.Client:
        """Crée un client Google Cloud Storage configuré pour fake-gcs-server"""
        return storage.Client(
            project="test-project"
        )

    def create_gcsfs_client(self) -> gcsfs.GCSFileSystem:
        """Crée un client gcsfs configuré pour fake-gcs-server"""
        return gcsfs.GCSFileSystem(
            project="test-project",
            endpoint_url=self.endpoint_url,
            token=None  # Pas d'authentification nécessaire avec fake-gcs-server
        )


class GcsTestDataManager:
    """Gestionnaire de données de test pour GCS"""

    def __init__(self, storage_client: storage.Client, gcsfs_client: gcsfs.GCSFileSystem):
        self.storage_client = storage_client
        self.gcsfs_client = gcsfs_client

    def create_test_bucket(self, bucket_name: str) -> storage.Bucket:
        """Crée un bucket de test"""
        try:
            bucket = self.storage_client.get_bucket(bucket_name)
            return bucket
        except Exception:
            return self.storage_client.create_bucket(bucket_name)

    def upload_test_confluence_data(self, bucket_name: str, prefix: str = "confluence/"):
        """Upload des données de test Confluence dans GCS"""
        bucket = self.create_test_bucket(bucket_name)

        # Données de test simulant des exports Confluence
        test_files = {
            f"{prefix}pages/page1.md": self._create_confluence_page_content("Page 1", "Content of page 1"),
            f"{prefix}pages/page2.html": self._create_confluence_html_content("Page 2", "<p>HTML content of page 2</p>"),
            f"{prefix}attachments/document.pdf": b"Mock PDF content",
            f"{prefix}attachments/image.png": b"Mock PNG content",
            f"{prefix}metadata/pages_metadata.json": self._create_metadata_json(),
        }

        for blob_path, content in test_files.items():
            blob = bucket.blob(blob_path)
            if isinstance(content, str):
                blob.upload_from_string(content)
            else:
                blob.upload_from_string(content)

        return test_files.keys()

    def _create_confluence_page_content(self, title: str, content: str) -> str:
        """Crée un contenu de page Confluence au format markdown"""
        return f"""# {title}

{content}

## Metadata
- Created: 2024-01-01T00:00:00Z
- Modified: 2024-01-02T00:00:00Z
- Space: TEST
"""

    def _create_confluence_html_content(self, title: str, content: str) -> str:
        """Crée un contenu de page Confluence au format HTML"""
        return f"""<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
</head>
<body>
    <h1>{title}</h1>
    {content}
</body>
</html>"""

    def _create_metadata_json(self) -> str:
        """Crée un fichier JSON de métadonnées"""
        import json
        metadata = {
            "export_date": "2024-01-01T00:00:00Z",
            "space_key": "TEST",
            "pages_count": 2,
            "attachments_count": 2
        }
        return json.dumps(metadata, indent=2)


@pytest.fixture(scope="session")
def fake_gcs_config():
    """Fixture pour la configuration fake-gcs-server"""
    config = FakeGcsTestConfig()
    config.setup_environment()
    return config


@pytest.fixture(scope="session")
def storage_client(fake_gcs_config):
    """Fixture pour le client Google Cloud Storage"""
    return fake_gcs_config.create_storage_client()


@pytest.fixture(scope="session")
def gcsfs_client(fake_gcs_config):
    """Fixture pour le client gcsfs"""
    return fake_gcs_config.create_gcsfs_client()


@pytest.fixture(scope="function")
def test_data_manager(storage_client, gcsfs_client):
    """Fixture pour le gestionnaire de données de test"""
    return GcsTestDataManager(storage_client, gcsfs_client)
