"""
Configuration simplifiée pour les tests avec fake-gcs-server en binaire
"""
import os
import subprocess
import time
import tempfile
import pytest
import requests
from pathlib import Path


class FakeGcsBinaryManager:
    """Gestionnaire pour fake-gcs-server en binaire"""

    def __init__(self, port=4443, scheme="http"):
        self.port = port
        self.scheme = scheme
        self.host = "localhost"
        self.url = f"{scheme}://{self.host}:{self.port}"
        self.process = None
        self.binary_path = None

    def find_binary(self):
        """Trouve le binaire fake-gcs-server"""
        # Cherche dans plusieurs emplacements
        possible_paths = [
            Path.home() / ".local/bin/fake-gcs-server",
            Path("/usr/local/bin/fake-gcs-server"),
            Path("./fake-gcs-server"),
            # Peut être installé via le script
        ]

        # Cherche aussi dans le PATH
        try:
            result = subprocess.run(["which", "fake-gcs-server"],
                                  capture_output=True, text=True, check=True)
            possible_paths.append(Path(result.stdout.strip()))
        except subprocess.CalledProcessError:
            pass

        for path in possible_paths:
            if path.exists() and path.is_file():
                self.binary_path = str(path)
                return True

        return False

    def start(self):
        """Démarre fake-gcs-server"""
        if not self.find_binary():
            pytest.skip("fake-gcs-server binary not found. Install with: ./scripts/fake-gcs-binary-manager.sh install")

        # Vérifie si déjà en cours d'exécution
        if self.is_running():
            return True

        # Démarre le serveur
        cmd = [
            self.binary_path,
            "-scheme", self.scheme,
            "-host", "0.0.0.0",
            "-port", str(self.port),
            "-public-host", f"{self.host}:{self.port}",
            "-backend", "memory"  # Utilise la mémoire pour les tests
        ]

        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )

            # Attente que le serveur soit prêt
            return self._wait_for_ready()

        except Exception as e:
            pytest.fail(f"Failed to start fake-gcs-server: {e}")

    def stop(self):
        """Arrête fake-gcs-server"""
        if self.process:
            try:
                if os.name != 'nt':
                    # Sur Unix, tue tout le groupe de processus
                    os.killpg(os.getpgid(self.process.pid), 15)
                else:
                    self.process.terminate()

                # Attente de l'arrêt
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Arrêt forcé si nécessaire
                    if os.name != 'nt':
                        os.killpg(os.getpgid(self.process.pid), 9)
                    else:
                        self.process.kill()

            except (ProcessLookupError, OSError):
                # Processus déjà arrêté
                pass

            self.process = None

    def is_running(self):
        """Vérifie si le serveur est en cours d'exécution"""
        try:
            response = requests.get(f"{self.url}/storage/v1/b", timeout=2)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False

    def _wait_for_ready(self, timeout=30):
        """Attend que le serveur soit prêt"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.is_running():
                return True

            if self.process and self.process.poll() is not None:
                # Le processus s'est arrêté
                stdout, stderr = self.process.communicate()
                pytest.fail(f"fake-gcs-server exited unexpectedly:\nSTDOUT: {stdout.decode()}\nSTDERR: {stderr.decode()}")

            time.sleep(0.5)

        return False

    def get_client_config(self):
        """Retourne la configuration pour les clients GCS"""
        return {
            "endpoint_url": self.url,
            "project": "test-project"
        }


@pytest.fixture(scope="session")
def fake_gcs_binary():
    """Fixture pour démarrer/arrêter fake-gcs-server en binaire"""
    manager = FakeGcsBinaryManager()

    # Configuration des variables d'environnement
    os.environ["STORAGE_EMULATOR_HOST"] = f"{manager.host}:{manager.port}"
    os.environ["GOOGLE_CLOUD_PROJECT"] = "test-project"

    # Démarrage
    if not manager.start():
        pytest.skip("Could not start fake-gcs-server")

    yield manager

    # Nettoyage
    manager.stop()


@pytest.fixture(scope="function")
def gcs_client_binary(fake_gcs_binary):
    """Client Google Cloud Storage configuré pour fake-gcs-server"""
    from google.cloud import storage

    client = storage.Client(project="test-project")
    return client


@pytest.fixture(scope="function")
def gcsfs_client_binary(fake_gcs_binary):
    """Client gcsfs configuré pour fake-gcs-server"""
    import gcsfs

    config = fake_gcs_binary.get_client_config()
    fs = gcsfs.GCSFileSystem(
        project=config["project"],
        endpoint_url=config["endpoint_url"],
        token=None
    )
    return fs


@pytest.fixture(scope="function")
def test_bucket_binary(gcs_client_binary):
    """Crée un bucket de test"""
    bucket_name = "test-confluence-bucket"

    try:
        bucket = gcs_client_binary.get_bucket(bucket_name)
    except Exception:
        bucket = gcs_client_binary.create_bucket(bucket_name)

    return bucket


class ConfluenceTestDataCreator:
    """Créateur de données de test Confluence pour fake-gcs-server"""

    def __init__(self, gcs_client):
        self.client = gcs_client

    def create_confluence_export(self, bucket_name, prefix="confluence/export/"):
        """Crée un export Confluence simulé"""
        bucket = self._get_or_create_bucket(bucket_name)

        test_files = {
            f"{prefix}pages/home.md": self._create_markdown_page("Accueil", "Contenu de la page d'accueil"),
            f"{prefix}pages/guide.html": self._create_html_page("Guide", "<p>Contenu du guide utilisateur</p>"),
            f"{prefix}pages/api.md": self._create_markdown_page("API", "Documentation de l'API REST"),
            f"{prefix}attachments/manual.pdf": b"Mock PDF content for manual",
            f"{prefix}attachments/schema.json": b'{"type": "object", "properties": {}}',
            f"{prefix}attachments/diagram.png": b"Mock PNG image data",
            f"{prefix}metadata/export_info.json": self._create_metadata(),
        }

        uploaded_files = []
        for file_path, content in test_files.items():
            blob = bucket.blob(file_path)
            if isinstance(content, str):
                blob.upload_from_string(content, content_type="text/plain")
            else:
                blob.upload_from_string(content)
            uploaded_files.append(file_path)

        return uploaded_files

    def _get_or_create_bucket(self, bucket_name):
        """Obtient ou crée un bucket"""
        try:
            return self.client.get_bucket(bucket_name)
        except Exception:
            return self.client.create_bucket(bucket_name)

    def _create_markdown_page(self, title, content):
        """Crée une page Markdown"""
        return f"""# {title}

{content}

## Métadonnées
- Créé: 2024-01-01T00:00:00Z
- Modifié: 2024-01-02T00:00:00Z
- Source: Confluence
"""

    def _create_html_page(self, title, content):
        """Crée une page HTML"""
        return f"""<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>{title}</h1>
    {content}
</body>
</html>"""

    def _create_metadata(self):
        """Crée des métadonnées d'export"""
        import json
        metadata = {
            "export_date": "2024-01-01T00:00:00Z",
            "space_key": "TEST",
            "total_pages": 3,
            "total_attachments": 3,
            "exporter": "confluence-test-exporter"
        }
        return json.dumps(metadata, indent=2)


@pytest.fixture(scope="function")
def confluence_test_data(gcs_client_binary):
    """Fixture pour créer des données de test Confluence"""
    creator = ConfluenceTestDataCreator(gcs_client_binary)
    return creator
