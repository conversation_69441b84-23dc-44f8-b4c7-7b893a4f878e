"""
Utilitaires pour simuler des exports Confluence dans GCS
"""
import json
from datetime import datetime, timezone
from typing import Dict, List, Any
from pathlib import Path


class ConfluenceExportSimulator:
    """Simule des exports Confluence stockés dans GCS"""

    def __init__(self, test_data_manager):
        self.test_data_manager = test_data_manager

    def create_confluence_space_export(self, bucket_name: str, space_key: str, export_date: str = None) -> str:
        """Crée un export complet d'un espace Confluence"""
        if not export_date:
            export_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")

        prefix = f"confluence-exports/{space_key}/{export_date}/"

        # Création du bucket
        bucket = self.test_data_manager.create_test_bucket(bucket_name)

        # Structure d'export Confluence typique
        export_structure = {
            # Pages principales
            f"{prefix}pages/": self._create_confluence_pages(),

            # Pièces jointes
            f"{prefix}attachments/": self._create_confluence_attachments(),

            # Métadonnées
            f"{prefix}metadata/": self._create_confluence_metadata(space_key, export_date),

            # Images et médias
            f"{prefix}media/": self._create_confluence_media(),
        }

        uploaded_files = []
        for base_path, files in export_structure.items():
            for filename, content in files.items():
                full_path = f"{base_path}{filename}"
                blob = bucket.blob(full_path)

                if isinstance(content, str):
                    blob.upload_from_string(content, content_type='text/plain')
                elif isinstance(content, bytes):
                    blob.upload_from_string(content)
                else:
                    blob.upload_from_string(json.dumps(content), content_type='application/json')

                uploaded_files.append(full_path)

        return prefix

    def _create_confluence_pages(self) -> Dict[str, str]:
        """Crée des pages Confluence d'exemple"""
        return {
            "home.md": self._create_markdown_page(
                "Accueil de l'espace",
                """
# Bienvenue dans l'espace de documentation

Cet espace contient toute la documentation technique du projet.

## Sections principales

- [Architecture](/pages/architecture.md)
- [Guide d'installation](/pages/installation.md)
- [API Reference](/pages/api.md)

## Dernières mises à jour

- 2024-01-01: Mise à jour de l'architecture
- 2024-01-02: Nouvelle documentation API
"""
            ),

            "architecture.md": self._create_markdown_page(
                "Architecture du système",
                """
# Architecture du système

## Vue d'ensemble

Le système est basé sur une architecture microservices avec les composants suivants:

### Composants principaux

1. **Service de chargement** - Gère l'import des données
2. **Service de traitement** - Traite les documents
3. **API Gateway** - Point d'entrée unifié

### Diagrammes

![Architecture](../media/architecture-diagram.png)

### Technologies utilisées

- Python 3.9+
- FastAPI
- Google Cloud Storage
- Confluence API
"""
            ),

            "installation.html": self._create_html_page(
                "Guide d'installation",
                """
<h1>Guide d'installation</h1>

<h2>Prérequis</h2>
<ul>
    <li>Python 3.9 ou supérieur</li>
    <li>Accès à Google Cloud Platform</li>
    <li>Clés API Confluence</li>
</ul>

<h2>Étapes d'installation</h2>
<ol>
    <li>Cloner le repository</li>
    <li>Installer les dépendances</li>
    <li>Configurer les variables d'environnement</li>
    <li>Lancer les tests</li>
</ol>

<h2>Configuration</h2>
<p>Copier le fichier <code>.env.example</code> vers <code>.env</code> et remplir les valeurs.</p>
"""
            ),

            "api.md": self._create_markdown_page(
                "Documentation API",
                """
# Documentation API

## Endpoints principaux

### GET /api/documents
Récupère la liste des documents

**Paramètres:**
- `space_key`: Clé de l'espace Confluence
- `limit`: Nombre maximum de résultats (défaut: 100)

**Réponse:**
```json
{
    "documents": [
        {
            "id": "doc_123",
            "name": "Document 1",
            "path": "gs://bucket/path/to/doc.md"
        }
    ]
}
```

### POST /api/process
Lance le traitement d'un document

**Corps de la requête:**
```json
{
    "document_id": "doc_123",
    "options": {
        "format": "markdown"
    }
}
```
"""
            )
        }

    def _create_confluence_attachments(self) -> Dict[str, bytes]:
        """Crée des pièces jointes d'exemple"""
        return {
            "technical-specs.pdf": self._create_mock_pdf(),
            "api-schema.json": self._create_mock_json_schema(),
            "example-data.csv": self._create_mock_csv(),
            "presentation.pptx": self._create_mock_powerpoint(),
        }

    def _create_confluence_metadata(self, space_key: str, export_date: str) -> Dict[str, Any]:
        """Crée les métadonnées de l'export Confluence"""
        return {
            "export_info.json": {
                "space_key": space_key,
                "export_date": export_date,
                "exporter_version": "1.0.0",
                "total_pages": 4,
                "total_attachments": 4,
                "export_format": "mixed",
                "settings": {
                    "include_attachments": True,
                    "include_comments": False,
                    "include_page_history": False
                }
            },

            "pages_index.json": {
                "pages": [
                    {
                        "id": "page_001",
                        "title": "Accueil de l'espace",
                        "path": "pages/home.md",
                        "created": "2024-01-01T10:00:00Z",
                        "modified": "2024-01-01T15:30:00Z",
                        "author": "admin",
                        "parent_id": None
                    },
                    {
                        "id": "page_002",
                        "title": "Architecture du système",
                        "path": "pages/architecture.md",
                        "created": "2024-01-01T11:00:00Z",
                        "modified": "2024-01-01T16:00:00Z",
                        "author": "developer",
                        "parent_id": "page_001"
                    },
                    {
                        "id": "page_003",
                        "title": "Guide d'installation",
                        "path": "pages/installation.html",
                        "created": "2024-01-01T12:00:00Z",
                        "modified": "2024-01-01T14:00:00Z",
                        "author": "admin",
                        "parent_id": "page_001"
                    },
                    {
                        "id": "page_004",
                        "title": "Documentation API",
                        "path": "pages/api.md",
                        "created": "2024-01-01T13:00:00Z",
                        "modified": "2024-01-01T17:00:00Z",
                        "author": "developer",
                        "parent_id": "page_001"
                    }
                ]
            }
        }

    def _create_confluence_media(self) -> Dict[str, bytes]:
        """Crée des fichiers média d'exemple"""
        return {
            "architecture-diagram.png": self._create_mock_image(),
            "logo.svg": self._create_mock_svg(),
            "screenshot.jpg": self._create_mock_jpeg(),
        }

    def _create_markdown_page(self, title: str, content: str) -> str:
        """Crée une page au format Markdown"""
        timestamp = datetime.now(timezone.utc).isoformat()
        return f"""---
title: {title}
created: {timestamp}
modified: {timestamp}
tags: [confluence, documentation]
---

{content.strip()}

---
*Exporté depuis Confluence le {timestamp}*
"""

    def _create_html_page(self, title: str, content: str) -> str:
        """Crée une page au format HTML"""
        timestamp = datetime.now(timezone.utc).isoformat()
        return f"""<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <meta name="created" content="{timestamp}">
    <meta name="modified" content="{timestamp}">
</head>
<body>
    {content.strip()}

    <footer>
        <p><em>Exporté depuis Confluence le {timestamp}</em></p>
    </footer>
</body>
</html>"""

    def _create_mock_pdf(self) -> bytes:
        """Crée un faux fichier PDF"""
        return b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF"

    def _create_mock_json_schema(self) -> bytes:
        """Crée un faux schéma JSON"""
        schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "metadata": {
                    "type": "object",
                    "properties": {
                        "created": {"type": "string", "format": "date-time"},
                        "modified": {"type": "string", "format": "date-time"}
                    }
                }
            },
            "required": ["id", "name"]
        }
        return json.dumps(schema, indent=2).encode('utf-8')

    def _create_mock_csv(self) -> bytes:
        """Crée un faux fichier CSV"""
        csv_content = """id,name,category,created_date
1,Document 1,Technical,2024-01-01
2,Document 2,User Guide,2024-01-02
3,Document 3,API Reference,2024-01-03
"""
        return csv_content.encode('utf-8')

    def _create_mock_powerpoint(self) -> bytes:
        """Crée un faux fichier PowerPoint"""
        # Signature PPTX basique
        return b"PK\x03\x04" + b"Mock PowerPoint content" + b"\x00" * 100

    def _create_mock_image(self) -> bytes:
        """Crée une fausse image PNG"""
        # Signature PNG basique
        return b"\x89PNG\r\n\x1a\n" + b"Mock PNG image data" + b"\x00" * 50

    def _create_mock_svg(self) -> bytes:
        """Crée un faux fichier SVG"""
        svg_content = """<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100">
    <circle cx="50" cy="50" r="40" fill="blue"/>
    <text x="50" y="55" text-anchor="middle" fill="white">Logo</text>
</svg>"""
        return svg_content.encode('utf-8')

    def _create_mock_jpeg(self) -> bytes:
        """Crée une fausse image JPEG"""
        # Signature JPEG basique
        return b"\xFF\xD8\xFF\xE0" + b"Mock JPEG image data" + b"\xFF\xD9"
