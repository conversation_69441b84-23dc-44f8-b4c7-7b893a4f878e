from dependency_injector import containers, providers
import os

from ..apicall.kbot_back_api import KbotB<PERSON><PERSON>pi
from ..apicall.kbot_embedding_api import Kbot<PERSON><PERSON>dding<PERSON>pi
from ..gcs.treatment_file_manager import TreatmentFileManager
from .basic.basic_mock_loader import BasicMockLoader
from ..loader.confluence.confluence_loader import ConfluenceLoader
from .confluence.mock_confluence_client import <PERSON>ckConfluenceClient
from .sharepoint.sharepoint_mock_loader import SharepointMockLoader
from ..loader.loader_manager import LoaderManager
from ..secret.secret_manager import ConfigWithSecret
from ..service.document_service import DocumentService
from ..service.loader_service import LoaderService
from ..service.schedule_service import ScheduleService
from ..service.sources_service import SourcesService

from ..loader.gcs.gcs_loader import GcsLoader
from .gcs.mock_gcs_client import setup_mock_client_with_test_data

def build_config():
    config = providers.Configuration()
    config.env.from_env("ENV", "local")
    config.version.from_env("VERSION", "dev")
    config.gcp_project_id.from_env("GCP_PROJECT_ID", "")
    # following default values are for unit tests launched from IDE
    config.path_to_secret_config.from_env("PATH_TO_SECRET_CONFIG", "")
    config.kbot_back_api_cloud_run_url.from_env("KBOT_BACK_API_CLOUD_RUN_URL", "https://kbot-back-api:8080")
    config.kbot_back_url.from_env("KBOT_BACK_API_URL", "https://kbot-back-api:8080")
    config.kbot_embedding_url.from_env("KBOT_EMBEDDING_API_URL", "https://kbot-embedding-api:8081")
    config.kbot_work_bucket_prefix.from_env("KBOT_WORK_BUCKET_PREFIX", "gs://mon_bucket-[perimeter_code]")
    config.okapi_url.from_env("OKAPI_URL", "")
    config.kbot_loadscheduler_client_id.from_env("KBOT_LOADSCHEDULER_CLIENT_ID", "kbot_loadscheduler_client_id")
    config.kbot_loadscheduler_client_secret.from_env(
        "KBOT_LOADSCHEDULER_CLIENT_SECRET", "kbot_loadscheduler_client_secret"
    )
    config.kbot_loadscheduler_client_scope.from_env("KBOT_LOADSCHEDULER_CLIENT_SCOPE", "")
    return config

def get_confluence_loader(config_with_secret):
    """
    Determines which Confluence loader to provide based on environment variables.
    This is the central switch for enabling/disabling Confluence mocking.
    """
    # Check if we should use the REAL Confluence client
    # This is true only if FORCE_REAL_CONFLUENCE is explicitly set to "true"
    # This logic is now the single point of control for mocking
    force_real = os.getenv("FORCE_REAL_CONFLUENCE", "false").lower() == "true"

    if force_real:
        print("✅ REAL MODE: Using the real ConfluenceLoader with a real ConfluenceClient.")
        # The loader will create its own real client. We pass `client=None`.
        return providers.Factory(ConfluenceLoader, config=config_with_secret, client=None)

    # --- MOCKING STRATEGY ---
    # By default, or if any mock flag is set, we use the real loader with a mock client.
    # This gives us the best of both worlds: we test the real loader's logic against
    # predictable, captured data.
    else:
        print("🔧 MOCK MODE: Using the real ConfluenceLoader with MockConfluenceClient.")
        # We create an instance of our mock client and inject it into the real loader.
        return providers.Factory(
            ConfluenceLoader,
            config=config_with_secret,
            client=providers.Singleton(MockConfluenceClient) # Inject the mock client here
        )

class MockContainer(containers.DeclarativeContainer):

    wiring_config = containers.WiringConfiguration(packages=["..route"])

    config = build_config()
    configWithSecret = providers.Singleton(ConfigWithSecret, config=config)

    kbot_back_api = providers.Factory(
        KbotBackApi,
        url=config.kbot_back_url,
        env=config.env,
        config_with_secret=configWithSecret,
        # In the mock container, we always inject None for the jwt provider
        orange_jwt=providers.Object(None),
    )

    kbot_embedding_api = providers.Factory(KbotEmbeddingApi, url=config.kbot_embedding_url, env=config.env)

    treatment_file_manager = providers.Factory(TreatmentFileManager, base_bucket=config.kbot_work_bucket_prefix)

    # Singleton ensures we use the same mock GCS instance everywhere.
    mock_gcs_client = providers.Singleton(setup_mock_client_with_test_data)

    # Instead of providing a mock loader, we provide the REAL loader
    # but with a MOCK client.
    gcs_loader = providers.Factory(
        GcsLoader,
        gcs_client=mock_gcs_client
    )

    basic_loader = providers.Factory(BasicMockLoader, config_with_secret=configWithSecret)

    sharepoint_loader = providers.Factory(SharepointMockLoader, config=configWithSecret)

    confluence_loader = get_confluence_loader(configWithSecret)

    loader_manager = providers.Factory(
        LoaderManager, gcs=gcs_loader, basic=basic_loader, sharepoint=sharepoint_loader, confluence=confluence_loader
    )

    sources_service = providers.Factory(
        SourcesService, kbot_back_api=kbot_back_api, treatment_file_manager=treatment_file_manager
    )

    loader_service = providers.Factory(
        LoaderService, loader_manager=loader_manager, treatment_file_manager=treatment_file_manager
    )

    document_service = providers.Factory(
        DocumentService, kbot_embedding_api=kbot_embedding_api, treatment_file_manager=treatment_file_manager
    )

    schedule_service = providers.Factory(
        ScheduleService,
        kbot_back_api=kbot_back_api,
        treatment_file_manager=treatment_file_manager,
        sources_service=sources_service,
        document_service=document_service,
        loader_service=loader_service,
    )
