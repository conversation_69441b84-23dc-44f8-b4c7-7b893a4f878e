"""Mock Embedding API Server

This module provides a mock implementation of the KBOT Embedding API
for local development and testing purposes.
"""

from fastapi import FastAP<PERSON>, HTTPException, Query
from typing import List, Dict, Any
import uvicorn
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Mock KBOT Embedding API",
    description="Mock implementation of the KBOT Embedding API for testing",
    version="1.0.0"
)

# Mock data storage
mock_documents: Dict[str, List[Dict[str, Any]]] = {}

@app.get("/embedded_documents")
async def get_embedded_documents(
    perimeter: str = Query(...),
    domain: str = Query(...),
    source: str = Query(...)
):
    """Get list of embedded documents for a given perimeter, domain, and source."""
    key = f"{perimeter}-{domain}-{source}"
    documents = mock_documents.get(key, [])

    logger.info(f"Getting documents for perimeter={perimeter}, domain={domain}, source={source}")
    logger.info(f"Returning {len(documents)} documents")

    return documents

@app.post("/embedded_documents/{perimeter}")
async def embed_document(perimeter: str, request_data: dict):
    """Embed a document for the given perimeter."""
    json_path = request_data.get('json_path')

    if not json_path:
        raise HTTPException(status_code=400, detail="json_path is required")

    logger.info(f"Embedding document for perimeter={perimeter}, json_path={json_path}")

    # Mock response - create a fake document
    mock_document = {
        "id": f"mock-doc-{len(mock_documents)}",
        "title": f"Mock Document from {json_path}",
        "url": f"mock://document/{perimeter}/doc",
        "source": "testconfluence27285",
        "domain": "CiblesRurales",
        "perimeter": perimeter,
        "created_at": "2025-07-14T10:00:00Z",
        "updated_at": "2025-07-14T10:00:00Z"
    }

    return mock_document

@app.delete("/embedded_documents")
async def remove_document(
    perimeter: str = Query(...),
    document_id: str = Query(...)
):
    """Remove an embedded document."""
    logger.info(f"Removing document {document_id} from perimeter {perimeter}")

    # Mock successful removal
    return {"status": "ok", "message": f"Document {document_id} removed"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": "mock-embedding-api"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8093, log_level="info")
