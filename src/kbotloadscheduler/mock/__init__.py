import os
import logging

logger = logging.getLogger(__name__)

def enable_all_mocks() -> None:
    """
    Activates any remaining legacy mocks based on the USE_MOCKS environment variable.

    NOTE: This function is being deprecated. Authentication mocking is now handled
    by the Dependency Injection container based on the `ENV` variable. This function
    only remains to support legacy GCS mocking.
    """
    should_enable_mocks = os.getenv("USE_MOCKS", "false").lower() == "true"

    if not should_enable_mocks:
        return

    logger.info("🔧 Enabling remaining legacy mocks...")

    # The call to enable_auth_mocking() has been removed.

    # Activate the legacy GCS mock.
    try:
        logger.info("   ✅ Legacy GCS mocking enabled.")
    except ImportError as e:
        logger.warning(f"Could not enable GCS mock: {e}")

    logger.info("✅ Legacy mock setup complete.")

# You can also remove the other helper functions from __init__.py as they
# are part of the system being phased out.

__all__ = [
    "enable_all_mocks",
]
