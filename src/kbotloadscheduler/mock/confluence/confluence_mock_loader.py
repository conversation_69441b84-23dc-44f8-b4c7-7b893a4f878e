"""
This mock simulates the behavior of the real ConfluenceLoader by:
- Using the same configuration parsing logic (build_confluence_config).
- Generating mock documents dynamically based on the configured spaces.
- Simulating the file download process in `get_document` by creating
  a dummy file and returning realistic metadata.
- Using the standard logging framework instead of print statements.
"""
import logging
import os
from datetime import datetime
from typing import Any, Dict, List

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean, Metadata
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.loader.abstract_loader import AbstractLoader
from kbotloadscheduler.loader.confluence.config.builder import build_config_from_source
from kbotloadscheduler.loader.confluence.config.schema import ConfluenceConfig


logger = logging.getLogger(__name__)


class ConfluenceMockLoader(AbstractLoader):
    """
    A high-fidelity mock loader for Confluence that mimics the real loader's
    behavior based on the source configuration.
    """

    def __init__(self, config: ConfigWithSecret):
        """Initialize the Confluence mock loader."""
        super().__init__("confluence")

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """
        Generates a list of mock documents based on the source configuration.
        """
        logger.info("CONFLUENCE MOCK: Generating mock document list.")
        return self._generate_mock_documents(source)

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """
        Simulates downloading a document by creating a dummy file and returning
        realistic metadata, including the file's location.
        """
        logger.info(f"CONFLUENCE MOCK: Simulating download for document '{document.id}'.")

        # Ensure the output directory exists
        os.makedirs(output_path, exist_ok=True)

        # Create a dummy file to simulate the download
        file_name = document.name.replace("/", "_") # Sanitize file name
        file_path = os.path.join(output_path, file_name)
        dummy_content = f"This is mock content for document '{document.name}' from source '{source.code}'."

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(dummy_content)
            file_size = os.path.getsize(file_path)
            logger.debug(f"CONFLUENCE MOCK: Created dummy file at '{file_path}' ({file_size} bytes).")
        except IOError as e:
            logger.error(f"CONFLUENCE MOCK: Failed to create dummy file at '{file_path}': {e}")
            # Return minimal metadata on failure
            return { Metadata.DOCUMENT_ID: document.id }


        # Return realistic metadata, similar to the real loader
        metadata = {
            Metadata.DOCUMENT_ID: document.id,
            Metadata.DOCUMENT_NAME: document.name,
            Metadata.LOCATION: file_path,
            "source_path": document.path,
            "modificationDate": document.modification_time.isoformat(),
            "size": file_size,
            "mime_type": "text/plain" # Mock mime type
        }
        return metadata

    def _generate_mock_documents(self, source: SourceBean) -> List[DocumentBean]:
        """
        Generates mock documents, including pages and attachments, by respecting
        the `include_attachments` and `file_extensions` settings from the config.
        """
        logger.debug("CONFLUENCE MOCK: Generating documents based on source configuration.")

        # Use the same configuration builder as the real loader for consistency
        try:
            config = build_config_from_source(source)
        except Exception as e:
            logger.error(f"CONFLUENCE MOCK: Failed to build config from source '{source.code}': {e}", exc_info=True)
            return []

        documents: List[DocumentBean] = []
        base_id_prefix = f"{source.domain_code}|{source.code}"

        if not config.basic.spaces:
            logger.warning(f"CONFLUENCE MOCK: No spaces configured for source '{source.code}'. Returning empty list.")
            return []

        # 1. Generate mock pages for each configured space
        for i, space_key in enumerate(config.basic.spaces):
            page_id = 123456 + i
            page = DocumentBean(
                id=f"{base_id_prefix}|{space_key}|{page_id}",
                name=f"Mock Page for {space_key}",
                path=f"/spaces/{space_key}/pages/{page_id}/Mock+Page+for+{space_key}",
                modification_time=datetime.now()
            )
            documents.append(page)

            # 2. Generate mock attachments for this page if enabled
            if config.attachments.include_attachments:
                self._add_mock_attachments(documents, page, config)

        logger.info(f"CONFLUENCE MOCK: Generated a total of {len(documents)} mock documents for source '{source.code}'.")
        return documents

    def _add_mock_attachments(self, doc_list: List[DocumentBean], page: DocumentBean, config: ConfluenceConfig):
        """Helper to generate and add mock attachments to the document list."""
        logger.debug(f"CONFLUENCE MOCK: Adding mock attachments for page '{page.id}'.")

        file_extensions_to_include = {ext.lower().lstrip('.') for ext in config.attachments.file_extensions}
        logger.debug(f"CONFLUENCE MOCK: Filtering attachments by extensions: {file_extensions_to_include}")

        mock_attachments_data = [
            ("technical_report.pdf", "pdf"),
            ("installation_guide.docx", "docx"),
            ("architecture.png", "png"),
            ("slides.pptx", "pptx"),
            ("data.xlsx", "xlsx"),
            ("archive.zip", "zip"), # This should be filtered out by default
            ("notes.txt", "txt"),   # This should also be filtered out
        ]

        attachment_count = 0
        page_id_parts = page.id.split("|")
        base_id_prefix = "|".join(page_id_parts[:-1]) # e.g., domain|source|space
        page_numeric_id = page_id_parts[-1]

        for filename, extension in mock_attachments_data:
            if extension in file_extensions_to_include:
                att = DocumentBean(
                    id=f"{base_id_prefix}|att_{extension}_{attachment_count:03d}",
                    name=filename,
                    path=f"/wiki/download/attachments/{page_numeric_id}/{filename}",
                    modification_time=datetime.now()
                )
                doc_list.append(att)
                attachment_count += 1
                logger.debug(f"CONFLUENCE MOCK: Added mock attachment: {filename}")
            else:
                logger.debug(f"CONFLUENCE MOCK: Skipped mock attachment '{filename}' due to extension filtering.")
