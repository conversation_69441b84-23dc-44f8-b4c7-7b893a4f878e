# --- START OF FILE mock_confluence_client.py ---

import json
import os
import re
from typing import Any, Dict, List

# Chemin vers le dossier contenant les réponses JSON capturées
# Adaptez ce chemin si nécessaire
MOCK_DATA_DIR = os.path.join(os.path.dirname(__file__), "mock_data")

class MockConfluenceClient:
    """
    Client Confluence simulé qui charge et sert des réponses API réelles capturées.
    Il implémente les mêmes méthodes que le vrai ConfluenceClient pour être
    facilement interchangeable dans les tests.
    """

    def __init__(self, *args, **kwargs):
        """
        Initialise le client simulé en chargeant toutes les données JSON
        depuis le répertoire de mock.
        """
        self.mock_data = self._load_mock_data()
        self.url = "https://mock.confluence.com"
        print(f"🔧 MOCK: MockConfluenceClient (dynamic) a été initialisé.")
        print(f"   -> {len(self.mock_data)} fichier(s) de données chargés depuis {MOCK_DATA_DIR}")

    def _load_mock_data(self) -> Dict[str, Any]:
        """Charge tous les fichiers .json du répertoire de données simulées."""
        data = {}
        if not os.path.isdir(MOCK_DATA_DIR):
            print(f"   -> AVERTISSEMENT: Le dossier de mock '{MOCK_DATA_DIR}' n'existe pas.")
            return data

        for filename in os.listdir(MOCK_DATA_DIR):
            if filename.endswith(".json"):
                # La clé est le nom du fichier sans l'extension
                key = filename.replace(".json", "")
                filepath = os.path.join(MOCK_DATA_DIR, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    data[key] = json.load(f)
        return data

    def search_content(self, cql: str, **kwargs) -> list[dict[str, Any]]:
        """
        Simule une recherche CQL. Tente de trouver une correspondance basée sur le cql.
        """
        print(f"🔧 MOCK API: Interception de search_content(cql='{cql[:50]}...')")

        # Logique simple : on cherche un fichier dont le nom contient 'cql_search'
        # Pour plus de précision, vous pouvez faire une correspondance sur le `space key`
        space_key_match = re.search(r"space\s*=\s*['\"](\w+)['\"]", cql)
        if space_key_match:
            space_key = space_key_match.group(1)
            mock_key = f"cql_search_space_{space_key}"
            if mock_key in self.mock_data:
                print(f"  -> Trouvé ! Utilisation des données de '{mock_key}.json'.")
                return self.mock_data[mock_key].get("results", [])

        # Fallback sur une clé générique si la recherche par espace échoue
        generic_key = "cql_search_default"
        if generic_key in self.mock_data:
            print(f"  -> Utilisation des données de fallback '{generic_key}.json'.")
            return self.mock_data[generic_key].get("results", [])

        print("  -> AVERTISSEMENT: Pas de données de mock trouvées pour cette recherche CQL. Retour d'une liste vide.")
        return []

    def get_all_attachments_for_pages(self, page_ids: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Simule la récupération des pièces jointes pour une liste de pages.
        """
        print(f"🔧 MOCK API: Interception de get_all_attachments_for_pages(page_ids={page_ids})")
        results = {}
        for page_id in page_ids:
            mock_key = f"attachments_for_page_{page_id}"
            if mock_key in self.mock_data:
                print(f"  -> Pour page {page_id}, trouvé ! Utilisation de '{mock_key}.json'.")
                # La réponse de l'API est un objet avec une clé "results"
                results[page_id] = self.mock_data[mock_key].get("results", [])
            else:
                print(f"  -> Pour page {page_id}, pas de données de mock trouvées. Retour d'une liste vide.")
                results[page_id] = []
        return results

    # Ajoutez d'autres méthodes du vrai client ici si nécessaire
    # Par exemple, get_page_content, get_attachment_content, etc.

    def close(self):
        """Méthode factice pour la compatibilité."""
        print("🔧 MOCK: Appel de la méthode close().")
        pass

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()