"""
GCS Mock Configuration for Local Testing

This module provides a way to enable GCS mocking in the main application
when the ENV environment variable is set to local or test.

Usage:
    Set environment variable: export USE_MOCKS=true
    Then start your application normally.
"""

import json
import os
from typing import Optional

# Global mock client instance
_mock_client = None


class MockConnection:
    """Mock connection object for GCS client"""
    def __init__(self):
        self.user_agent = "mock-gcs-client/1.0"
        self.API_BASE_URL = "https://mock-storage.googleapis.com"


class MockGCSForLocalTesting:
    """Simplified GCS mock for local testing without pytest fixtures"""

    def __init__(self):
        self.buckets = {}
        self.blobs = {}
        # Add attributes that Google Cloud Storage library expects
        self._http = None
        self.project = "mock-project"
        self._connection = MockConnection()
        self._extra_headers = {}

    def get_bucket(self, bucket_name: str):
        """Get or create a mock bucket"""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = MockBucket(bucket_name, self)
        return self.buckets[bucket_name]

    def add_test_data(self, bucket_name: str, blob_path: str, content: str):
        """Add test data to the mock"""
        bucket = self.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)
        blob.upload_from_string(content)
        return blob


class MockBucket:
    """Mock GCS bucket"""

    def __init__(self, name: str, client):
        self.name = name
        self.client = client
        self.blobs = {}
        self.path = f"/b/{name}"  # Add path attribute expected by GCS library
        self.user_project = None  # Add user_project attribute

    def blob(self, blob_name: str):
        """Get or create a mock blob"""
        if blob_name not in self.blobs:
            self.blobs[blob_name] = MockBlob(blob_name, self)
        return self.blobs[blob_name]

    def get_blob(self, blob_name: str):
        """Get existing blob or None"""
        return self.blobs.get(blob_name)


class MockBlob:
    """Mock GCS blob with comprehensive method support"""

    def __init__(self, name: str, bucket):
        self.name = name
        self.bucket = bucket
        self.content = ""
        self._exists = False
        # Add attributes that Google Cloud Storage library expects
        self.size = 0
        self.content_type = "text/plain"

    def exists(self, client=None):
        """Check if blob exists"""
        return self._exists

    def download_as_bytes(self):
        """Download content as bytes"""
        return self.content.encode('utf-8') if isinstance(self.content, str) else self.content

    def download_as_string(self):
        """Download content as string (for compatibility with existing code)"""
        return self.content

    def upload_from_string(self, content: str, content_type=None):
        """Upload content from string - simplified mock implementation"""
        self.content = content
        self._exists = True
        self.size = len(content.encode('utf-8')) if isinstance(content, str) else len(content)
        if content_type:
            self.content_type = content_type

    def upload_from_file(self, file_obj, content_type=None, client=None):
        """Upload content from file object"""
        content = file_obj.read()
        if isinstance(content, bytes):
            content = content.decode('utf-8')
        self.upload_from_string(content, content_type)

    # Override all the internal methods that might be called during upload
    def __getattr__(self, name):
        """Catch-all for any missing attributes/methods"""
        if name.startswith('_'):
            # For internal methods, return a no-op function
            def mock_method(*args, **kwargs):
                return None
            return mock_method
        raise AttributeError(f"MockBlob has no attribute '{name}'")


def setup_mock_client_with_test_data():
    """Setup mock client with predefined test data for different source types"""
    mock_client = MockGCSForLocalTesting()

    # Check if we're in real Confluence mode
    real_confluence_mode = os.getenv("REAL_CONFLUENCE_MODE", "false").lower() == "true"

    if real_confluence_mode:
        print("🔧 Real Confluence mode enabled - using integration test configuration")
        # Use the exact same configuration as the integration test
        real_confluence_config = {
            "sourceCurrentLabel": "test confluence",
            "sourceType": "confluence",
            "confluence_url": "https://espace.agir.orange.com/",
            "space_key": "VODCASTV",  # Will be transformed to spaces: ["VODCASTV"]
            "labels": "ravenne, rag",  # Will be transformed to labels: ["ravenne", "rag"]
            "child_page_depth": "15",
            # Enable attachments like integration test
            "include_attachments": True,  # Enable for testing
            "file_extensions": ["pdf", "docx", "xlsx", "pptx", "txt", "png", "jpg", "drawio"]
        }

        # Create SourceBean that matches integration test exactly
        source_bean_data = {
            "id": 1,
            "code": "vodcastv_ravenne_test",
            "label": "VODCASTV Ravenne Test",
            "src_type": "confluence",
            "configuration": json.dumps(real_confluence_config),
            "last_load_time": 1750069757,
            "next_load_time": 1750156157,
            "load_interval": 24,
            "force_embedding": False,
            "domain_code": "test",
            "perimeter_code": "tests"
        }

        # Add this configuration to multiple test buckets
        test_buckets = [
            "mock-bucket-tests",
            "mock-bucket-mktsearch",  # Keep existing one working
            "test-bucket"  # For simple test URLs
        ]

        for bucket_name in test_buckets:
            # For mktsearch, use mktsearch perimeter_code
            if "mktsearch" in bucket_name:
                mktsearch_source_bean_data = source_bean_data.copy()
                mktsearch_source_bean_data["perimeter_code"] = "mktsearch"
                mktsearch_source_bean_data["code"] = "testconfluence_mktsearch"
                mktsearch_source_bean_data["label"] = "Marketing Search Confluence"
                mktsearch_source_bean_data["domain_code"] = "MarketingSearch"
                data_to_use = mktsearch_source_bean_data
            else:
                data_to_use = source_bean_data

            mock_client.add_test_data(
                bucket_name,
                "test-data/getlist.json",
                json.dumps(data_to_use, indent=2)
            )
            mock_client.add_test_data(
                bucket_name,
                "test-confluence.json",
                json.dumps(data_to_use, indent=2)
            )
            print(f"📁 Added REAL Confluence config: gs://{bucket_name}/test-data/getlist.json")
            print(f"📁 Added REAL Confluence config: gs://{bucket_name}/test-confluence.json")

        return mock_client

    # Define different source types with their specific configurations
    source_type_configs = {
        "confluence": {
            "confluence_url": "https://espace.agir.orange.com/",
            "space_key": "VODCASTV",  # Utiliser l'espace qui fonctionne
            "labels": "",  # Try without labels first to reduce scope
            "child_page_depth": "5",  # Reduce depth to avoid timeout
            "include_attachments": "false",  # Disable attachments to reduce load
            "max_results": 10,  # Reduce max results
            "export_format": "markdown"
        },
        "confluence_mktsearch": {
            "sourceCurrentLabel": "test confluence",
            "sourceType": "confluence",
            "confluence_url": "https://espace.agir.orange.com/",
            "space_key": "VODCASTV",
            "labels": "ravenne, rag",
            "child_page_depth": "15",
            "include_attachments": "true"
        },
        "confluence_alternative": {
            "confluence_url": "https://espace.agir.orange.com/",
            "space_key": "LEGACY_SPACE",  # Alternative space for testing
            "labels": "",  # No specific labels
            "child_page_depth": "10",
            "include_attachments": "false",
            "max_results": 50,
            "export_format": "markdown"
        },
        "sharepoint": {
            "sharepoint_url": "https://company.sharepoint.com/sites/knowledge",
            "site_id": "abc123-def456-ghi789",
            "drive_id": "b!xyz789",
            "folder_path": "/Shared Documents/Knowledge Base",
            "include_subfolders": "true",
            "file_types": "docx,xlsx,pptx,pdf"
        }
    }

    # Add test data to multiple buckets for different perimeters and source types
    perimeter_configs = {
        "ebotman": {
            "code": "testconfluence27284",
            "label": "E-Botman Confluence",
            "domain_code": "EBotman",
            "src_type": "confluence",
            "config_key": "confluence",
            "id": 1079611284
        },
        "mktsearch": {
            "code": "testconfluence27284",
            "label": "test confluence",
            "domain_code": "CiblesRurales",
            "src_type": "confluence",
            "config_key": "confluence_mktsearch",
            "id": 1079611284
        },
        "sharepoint": {
            "code": "testsharepoint12345",
            "label": "Marketing Search SharePoint",
            "domain_code": "MarketingSearch",
            "src_type": "sharepoint",
            "config_key": "sharepoint",
            "id": 1079611287
        }
    }

    for perimeter, config in perimeter_configs.items():
        bucket_name = f"mock-bucket-{perimeter}"
        src_type = config["src_type"]
        config_key = config.get("config_key", src_type)  # Use config_key if available, fallback to src_type

        # Create SourceBean-compatible structure for getlist.json
        # This structure matches what SourceBean expects
        source_bean_data = {
            "id": config["id"],
            "code": config["code"],
            "label": config["label"],
            "src_type": src_type,
            "perimeter_code": perimeter,  # Add missing perimeter_code field
            "configuration": json.dumps(source_type_configs[config_key], separators=(',', ':')),  # JSON string as expected by SourceBean
            "last_load_time": 1750069757,
            "next_load_time": 1750156157,
            "load_interval": 24,
            "force_embedding": False,
            "domain_id": 1,
            "domain_code": config["domain_code"]
        }

        mock_client.add_test_data(
            bucket_name,
            "test-data/getlist.json",
            json.dumps(source_bean_data, indent=2)
        )

        # Also add data to the path you prefer to use
        mock_client.add_test_data(
            bucket_name,
            "getlist/getlist.json",
            json.dumps(source_bean_data, indent=2)
        )

        print(f"📁 Added mock data: gs://{bucket_name}/test-data/getlist.json ({src_type})")
        print(f"📁 Added mock data: gs://{bucket_name}/getlist/getlist.json ({src_type})")

    return mock_client


def enable_gcs_mocking():
    """
    Enable GCS mocking by patching the google.cloud.storage module.
    This is called automatically if USE_MOCKS=true is set.
    """
    global _mock_client

    if _mock_client is None:
        print("🔧 Enabling GCS mocking for local testing...")
        _mock_client = setup_mock_client_with_test_data()

        # Patch the storage module
        import google.cloud.storage as storage

        # Create a mock Client class that returns our mock client
        class MockClientClass:
            def __init__(self, *args, **kwargs):
                # Add all the attributes that the real Client has
                self.project = "mock-project"
                self._connection = MockConnection()
                self._extra_headers = {}
                self._http = None

            def get_bucket(self, bucket_name):
                return _mock_client.get_bucket(bucket_name)

            def bucket(self, bucket_name):
                return _mock_client.get_bucket(bucket_name)

            # Add any other methods that might be called
            def __getattr__(self, name):
                # For any missing methods, delegate to the mock client
                return getattr(_mock_client, name, lambda *args, **kwargs: None)

        # Replace the Client class
        storage.Client = MockClientClass

        print("✅ GCS mocking enabled! All storage.Client() calls will use mock data.")
        print("📋 Available test buckets:")
        for bucket_name in _mock_client.buckets.keys():
            print(f"   - gs://{bucket_name}/test-data/getlist.json")

    return _mock_client


def get_mock_client() -> Optional[MockGCSForLocalTesting]:
    """Get the current mock client instance"""
    return _mock_client


# Auto-enable mocking if environment variable is set
if os.getenv("USE_MOCKS", "false").lower() == "true":
    enable_gcs_mocking()
