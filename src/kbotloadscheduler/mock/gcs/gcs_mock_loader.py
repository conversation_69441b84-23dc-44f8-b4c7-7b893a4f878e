from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.abstract_loader import AbstractLoader


class GcsMockLoader(AbstractLoader):
    """Loader pour google cloud storage"""

    def __init__(self):
        super().__init__("gcs")

    def get_document_list(self, source: SourceBean) -> list[DocumentBean]:
        print("🔧 GcsLoader.get_document_list() called in mock mode - returning empty list")
        return []

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        print("🔧 GcsLoader.get_document() called in mock mode - returning empty metadata")
        return {}
