import os
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict
from . import gcs_utils
from .gcs_utils import tidy_file_name
from ..bean.beans import SourceBean, DocumentBean, Metadata, LOAD_DATE_FORMAT
from ..logging.load_scheduler_logging import log_message


class TreatmentFileManager:

    GET_LIST = "getlist"
    LIST = "list"
    GET_DOC = "getdoc"
    DOCS = "docs"
    REMOVE_DOC = "removedoc"
    KNOWN_FILE_TYPES = [GET_LIST, LIST, DOCS, REMOVE_DOC]
    DOCS_DONE = "docs_done"
    DOCS_ERROR = "docs_error"

    JSON = ".json"
    JSON_REGEXP = re.compile(r"\.json$")
    IN_PROGRESS = ".inprogress"
    DONE = ".done"
    IN_ERROR = ".error"
    METADATA = ".metadata"
    FILE_STATUS_IN_PROGRESS = "in_progress"
    FILE_STATUS_IN_ERROR = "in_error"
    FILE_STATUS_DONE = "done"
    ERROR_LOG = ".error.log"
    TOKEN_PREFIX = "tokens"  # nosec B105
    GETFILE = ".getfile"

    def __init__(self, base_bucket: str):
        self._base_bucket = base_bucket

    def get_perimeter_bucket(self, perimeter_code):
        result = self._base_bucket.replace('[perimeter_code]', perimeter_code)
        print(f"🔧 get_perimeter_bucket('{perimeter_code}')")
        print(f"   📋 _base_bucket: {self._base_bucket}")
        print(f"   🪣 result: {result}")
        return result

    @staticmethod
    def extract_info_from_file_name(file_name):
        """
        Extrait des informations structurées à partir du chemin d'un fichier GCS.

        Cette méthode repose sur une convention de nommage pour les chemins de fichiers.
        Pour fonctionner, le chemin doit contenir une séquence spécifique de "dossiers" :
        `{load_date}/{domain_code}/{source_code}/{file_type}`

        **Principe de fonctionnement :**

        La méthode **recherche** la première occurrence d'un composant de chemin qui est une chaîne
        de 12 chiffres (format `YYYYMMDDHHMM`). Ce composant `{load_date}` sert de "point d'ancrage".
        Toute la structure de répertoires qui précède ce composant est ignorée.

        Une fois le `{load_date}` trouvé, la méthode s'attend à trouver :
        1.  `{domain_code}` : Le composant qui suit immédiatement.
        2.  `{source_code}` : Le composant suivant.
        3.  `{file_type}` : Le composant d'après (ex: 'getlist', 'getdoc', 'docs').

        **Comportement en cas de succès :**

        *   Exemple de chemin valide :
            `gs://bucket/un/prefixe/quelconque/202401151030/mon_domaine/ma_source/getdoc/document.json`
        *   Résultat :
            `['202401151030', 'mon_domaine', 'ma_source', 'getdoc', 'document.json']`

        **⚠️ Avertissement sur le comportement en cas d'échec :**

        Si aucun composant de 12 chiffres n'est trouvé dans le chemin, **la méthode retourne
        une liste de 4 éléments `None`** : `[None, None, None, None]`.
        Ceci cause une erreur `ValueError: not enough values to unpack (expected 5, got 4)`.
        Il est donc impératif que tout chemin traité contienne une date au format `YYYYMMDDHHMM`.

        *   Exemple de chemin invalide :
            `gs://bucket/test/mon_domaine/ma_source/getdoc/document.json`
        *   Résultat :
            `[None, None, None, None]`
        """

        [_, file] = gcs_utils.get_bucket_and_object_name(file_name)
        parts = file.split('/')
        length = len(parts)
        pattern = re.compile(r'\d{12}')
        for index, part in enumerate(parts):
            if index < length-3 and re.fullmatch(pattern, part):
                # Return load_date, domain, source and file_type and file_sig (the file signature)
                file_sig = "/".join(parts[index + 1:])
                return [part, parts[index+1], parts[index+2], parts[index+3], file_sig]
        return [None, None, None, None]

    def build_directory_name(self, perimeter_code: str, load_date: str,
                             domain_code: str, source_code: str, file_type: str):
        bucket_name = self.get_perimeter_bucket(perimeter_code)
        result = os.path.join(bucket_name, load_date, domain_code, source_code, file_type)
        print(f"🔧 build_directory_name('{perimeter_code}', '{load_date}', '{domain_code}', '{source_code}', '{file_type}')")
        print(f"   🪣 bucket_name: {bucket_name}")
        print(f"   📂 result: {result}")
        return result

    def write_get_list(self, load_date: str, source: SourceBean):
        json_source = json.dumps(source.__dict__)
        get_list_directory = self.build_directory_name(source.perimeter_code, load_date,
                                                       source.domain_code, source.code, self.GET_LIST)
        get_list_file = os.path.join(get_list_directory, f'{self.GET_LIST}{self.JSON}')
        [bucket, file] = gcs_utils.get_bucket_and_object_name(get_list_file)
        gcs_utils.create_file_with_content(bucket, file, json_source)

    @staticmethod
    def read_get_list(get_list_file: str) -> SourceBean | None:
        if gcs_utils.exists_file_gcs(get_list_file):
            return SourceBean(**gcs_utils.get_json_from(get_list_file, '{}'))
        return None

    def write_document_list(self, load_date: str, source: SourceBean, documents: List[DocumentBean],
                            file_type: str = 'list') -> List[dict]:
        # 🔧 LOG: Début de write_document_list
        print(f"🔧 TreatmentFileManager.write_document_list() START")
        print(f"   📅 load_date: {load_date}")
        print(f"   🏷️  source.perimeter_code: {source.perimeter_code}")
        print(f"   🏢 source.domain_code: {source.domain_code}")
        print(f"   📦 source.code: {source.code}")
        print(f"   📁 file_type: {file_type}")
        print(f"   📄 documents count: {len(documents)}")
        print(f"   🪣 base_bucket: {self._base_bucket}")

        serialized_documents = [document.to_serializable_dict() for document in documents]
        sorted_serialized_documents = sorted(serialized_documents, key=lambda d: d['id'])
        dict_list = {
            'source': source.__dict__,
            'documents': sorted_serialized_documents
        }
        json_list = json.dumps(dict_list)

        # 🔧 LOG: Construction du chemin
        list_directory = self.build_directory_name(source.perimeter_code, load_date,
                                                   source.domain_code, source.code, file_type)
        print(f"   📂 list_directory: {list_directory}")

        list_file = os.path.join(list_directory, f'{file_type}{self.JSON}')
        print(f"   📄 list_file (full path): {list_file}")

        [bucket, file] = gcs_utils.get_bucket_and_object_name(list_file)
        print(f"   🪣 bucket: {bucket}")
        print(f"   📄 file (object name): {file}")
        print(f"   📊 JSON content size: {len(json_list)} chars")

        # 🔧 LOG: Appel à create_file_with_content
        print(f"   🚀 Calling gcs_utils.create_file_with_content('{bucket}', '{file}', ...)")
        gcs_utils.create_file_with_content(bucket, file, json_list)
        print(f"   ✅ create_file_with_content completed")
        print(f"🔧 TreatmentFileManager.write_document_list() END")

        return sorted_serialized_documents

    def read_document_list(self, document_list_file):
        if gcs_utils.exists_file_gcs(document_list_file):
            json_list = gcs_utils.get_json_from(document_list_file, '{}')
            source = SourceBean(**json_list.get('source', {}))
            documents = [DocumentBean.from_serializable_dict(document) for document in json_list.get('documents', [])]
            return {'source': source, 'documents': documents}
        return None

    def write_document_get_file(self, load_date: str, source: SourceBean, document: DocumentBean) -> dict:
        serialized_document = document.to_serializable_dict()

        dict_document = {
            'source': source.__dict__,
            'document': serialized_document
        }
        json_doc = json.dumps(dict_document)
        site_name = source.get_expected_conf('site_name')
        relative_directory = source.get_expected_conf('relative_directory')
        site_relative_directory = f'/sites/{site_name}/{relative_directory}/'
        document_path = self.build_directory_name(
            source.perimeter_code, load_date, source.domain_code, source.code, TreatmentFileManager.GET_DOC)
        tidy_relative_path = tidy_file_name(document.name.replace(site_relative_directory, ''))
        doc_get_file = os.path.join(document_path, f'{tidy_relative_path}.{self.GET_DOC}{self.JSON}')
        [bucket, file] = gcs_utils.get_bucket_and_object_name(doc_get_file)
        gcs_utils.create_file_with_content(bucket, file, json_doc)
        return serialized_document

    @staticmethod
    def read_document_get_file(document_get_file):
        print("document %s" % document_get_file)
        if gcs_utils.exists_file_gcs(document_get_file):
            json = gcs_utils.get_json_from(document_get_file, '{}')
            source = SourceBean(**json.get('source', {}))
            document = DocumentBean.from_serializable_dict(json.get('document', {}))
            return {'source': source, 'document': document}
        return None

    def write_document_metadata(self, metadata: dict) -> dict:
        location = metadata.get(Metadata.LOCATION)
        metadata_path = f'{location}{self.METADATA}{self.JSON}'
        [bucket, file] = gcs_utils.get_bucket_and_object_name(metadata_path)
        serialized_md = Metadata.serialize(metadata)
        metadata_json = json.dumps(serialized_md)
        gcs_utils.create_file_with_content(bucket, file, metadata_json)
        return serialized_md

    def read_document_metadata(self, metadata_file: str) -> dict | None:
        if gcs_utils.exists_file_gcs(metadata_file):
            metadata_json = gcs_utils.get_json_from(metadata_file, '{}')
            return Metadata.unserialize(metadata_json)
        return None

    def set_in_progress(self, treatment_file_json):
        treatment_file_in_progress = re.sub(self.JSON_REGEXP, self.IN_PROGRESS, treatment_file_json)
        [bucket_name, object_name] = gcs_utils.get_bucket_and_object_name(treatment_file_in_progress)
        gcs_utils.create_file_with_content(bucket_name, object_name, 'in progress')

    def unset_in_progress(self, treatment_file_json):
        treatment_file_in_progress = re.sub(self.JSON_REGEXP, self.IN_PROGRESS, treatment_file_json)
        if gcs_utils.exists_file_gcs(treatment_file_in_progress):
            gcs_utils.delete_file(treatment_file_in_progress)

    def set_done(self, treatment_file_json):
        treatment_file_done = re.sub(self.JSON_REGEXP, self.DONE, treatment_file_json)
        [bucket_name, object_name] = gcs_utils.get_bucket_and_object_name(treatment_file_done)
        gcs_utils.create_file_with_content(bucket_name, object_name, 'done')
        self.unset_in_progress(treatment_file_json)

    def set_in_error(self, treatment_file_json, content):
        treatment_file_error = re.sub(self.JSON_REGEXP, self.IN_ERROR, treatment_file_json)
        [bucket_name, object_name] = gcs_utils.get_bucket_and_object_name(treatment_file_error)
        gcs_utils.create_file_with_content(bucket_name, object_name, content)
        self.unset_in_progress(treatment_file_json)

    def move_embedded_document_to_done(self, perimeter_code: str, metadata_json: dict):
        gcs_document_path = metadata_json.get(Metadata.LOCATION)
        [load_date, domain_code, source_code, _, _] = self.extract_info_from_file_name(gcs_document_path)
        gcs_docs_directory = self.build_directory_name(perimeter_code, load_date,
                                                       domain_code, source_code, self.DOCS)
        gcs_done_directory = self.build_directory_name(perimeter_code, load_date,
                                                       domain_code, source_code, self.DOCS_DONE)
        if gcs_document_path and gcs_utils.exists_file_gcs(gcs_document_path):
            gcs_document_blob = gcs_utils.get_gcs_blob(gcs_document_path)
            gcs_destination_path = gcs_document_path.replace(gcs_docs_directory, gcs_done_directory)
            gcs_utils.move_file_to_destination(gcs_document_blob, gcs_destination_path)
            gcs_metadata_path = f'{gcs_document_path}{self.METADATA}{self.JSON}'
            if gcs_metadata_path and gcs_utils.exists_file_gcs(gcs_metadata_path):
                gcs_metadata_blob = gcs_utils.get_gcs_blob(gcs_metadata_path)
                gcs_md_destination_path = f'{gcs_destination_path}{self.METADATA}{self.JSON}'
                gcs_utils.move_file_to_destination(gcs_metadata_blob, gcs_md_destination_path)

    def move_embedded_document_to_error(self, perimeter_code, metadata_json, error):
        gcs_document_path = metadata_json.get(Metadata.LOCATION)
        [load_date, domain_code, source_code, _, _] = self.extract_info_from_file_name(gcs_document_path)
        gcs_docs_directory = self.build_directory_name(perimeter_code, load_date,
                                                       domain_code, source_code, self.DOCS)
        gcs_error_directory = self.build_directory_name(perimeter_code, load_date,
                                                        domain_code, source_code, self.DOCS_ERROR)
        log_message(perimeter_code, "move file to error docs/error " + gcs_docs_directory
                    + " ==> " + gcs_error_directory)
        if gcs_document_path and gcs_utils.exists_file_gcs(gcs_document_path):
            gcs_document_blob = gcs_utils.get_gcs_blob(gcs_document_path)
            gcs_destination_path = gcs_document_path.replace(gcs_docs_directory, gcs_error_directory)
            log_message(perimeter_code, "move file to error " + gcs_document_path+" ==> "+gcs_destination_path)
            gcs_utils.move_file_to_destination(gcs_document_blob, gcs_destination_path)
            gcs_error_path = f'{gcs_destination_path}{self.ERROR_LOG}'
            [bucket, file] = gcs_utils.get_bucket_and_object_name(gcs_error_path)
            error_log = {**Metadata.serialize(metadata_json), 'error': error}
            gcs_utils.create_file_with_content(bucket, file, json.dumps(error_log))
            gcs_metadata_path = f'{gcs_document_path}{self.METADATA}{self.JSON}'
            if gcs_metadata_path and gcs_utils.exists_file_gcs(gcs_metadata_path):
                gcs_metadata_blob = gcs_utils.get_gcs_blob(gcs_metadata_path)
                gcs_md_destination_path = f'{gcs_destination_path}{self.METADATA}{self.JSON}'
                gcs_utils.move_file_to_destination(gcs_metadata_blob, gcs_md_destination_path)

    def get_load_dates(self, perimeter_code: str, treatment_date: datetime) -> List[str]:
        return self.__get_dates_in_range__(perimeter_code, treatment_date - timedelta(days=1), treatment_date)

    def get_dates_to_purge(self, perimeter_code: str, treatment_date: datetime) -> List[str]:
        return self.__get_dates_in_range__(perimeter_code, datetime(1970, 1, 1),
                                           treatment_date - timedelta(days=2))

    def __get_dates_in_range__(self, perimeter_code: str, start_date: datetime, end_date: datetime) -> List[str]:
        bucket_name = gcs_utils.get_bucket_and_object_name(self.get_perimeter_bucket(perimeter_code))[0]
        list_blobs = gcs_utils.list_gcs_direct_child_blob(bucket_name)
        list_blobs.sort()
        load_date_list = []
        pattern = re.compile(r'\d{12}')
        for blob_prefix in list_blobs:
            blob_name_date = blob_prefix[0:12]
            if re.fullmatch(pattern, blob_name_date):
                load_date = datetime.strptime(blob_name_date, LOAD_DATE_FORMAT)
                if start_date < load_date < end_date:
                    load_date_list.append(blob_name_date)
        return load_date_list

    def get_treatments_files(self, perimeter_code: str, load_date: str) -> Dict[str, Dict[str, str]]:
        bucket_name = gcs_utils.get_bucket_and_object_name(self.get_perimeter_bucket(perimeter_code))[0]
        gcs_uri = f'gs://{bucket_name}/{load_date}'
        all_blobs = gcs_utils.list_gcs_blob(gcs_uri)
        files_by_type = {}
        for blob in all_blobs:
            key, file_value = self.get_file_info(bucket_name, blob)
            if file_value is not None:
                value = files_by_type.get(key, {})
                files_by_type[key] = {**value, **file_value}
        return files_by_type

    def get_file_info(self, bucket_name, blob):
        blob_path = f'gs://{bucket_name}/{blob.name}'
        [blob_load_date, domain, source, file_type, file_sig] = self.extract_info_from_file_name(blob_path)
        key = '|'.join([domain, source, file_type])
        common_value = {
            'load_date': blob_load_date,
            'domain': domain,
            'source': source,
            'file_type': file_type,
            'file_sig': file_sig
        }

        file_value = None
        if file_type == self.DOCS or file_type == self.GET_DOC:
            file_value = self.parse_docs_files(blob_path)
            extra_key = blob_path \
                .replace(os.path.join(f'gs://{bucket_name}/{blob_load_date}/{domain}/{source}/{file_type}/'), '') \
                .replace('.metadata.json', '').replace('.metadata.inprogress', '').replace('.metadata.done', '') \
                .replace('.getdoc.json', '').replace('.getdoc.inprogress', '').replace('.getdoc.done', '')
            key = key + '|' + extra_key
        elif file_type in self.KNOWN_FILE_TYPES:
            file_value = self.parse_standard_files(file_type, blob_path)
        return_value = None
        if file_value is not None:
            return_value = {**common_value, **file_value}
        return key, return_value

    @classmethod
    def parse_standard_files(cls, file_type, blob_path):
        file_name = os.path.basename(blob_path)
        if file_name == file_type+cls.JSON:
            return {'file': blob_path}
        elif file_name == file_type + cls.IN_PROGRESS:
            return {cls.FILE_STATUS_IN_PROGRESS: True}
        elif file_name == file_type+cls.DONE:
            return {cls.FILE_STATUS_DONE: True}
        elif file_name == file_type + cls.IN_ERROR:
            return {cls.FILE_STATUS_IN_ERROR: True}
        return None

    @classmethod
    def parse_docs_files(cls, blob_path):
        file_name = os.path.basename(blob_path)
        if file_name.endswith(cls.METADATA + cls.JSON) or file_name.endswith(cls.GET_DOC + cls.JSON):
            return {'file': blob_path}
        elif file_name.endswith(cls.METADATA + cls.IN_PROGRESS) or file_name.endswith(cls.GET_DOC + cls.IN_PROGRESS):
            return {cls.FILE_STATUS_IN_PROGRESS: True}
        elif file_name.endswith(cls.METADATA + cls.DONE) or file_name.endswith(cls.GET_DOC + cls.DONE):
            return {cls.FILE_STATUS_DONE: True}
        elif file_name.endswith(cls.METADATA + cls.IN_ERROR) or file_name.endswith(cls.GET_DOC + cls.IN_ERROR):
            return {cls.FILE_STATUS_IN_ERROR: True}
        return None

    def purge_old_treatments_files(self, perimeter_code: str, treatment_date: datetime):
        bucket = self.get_perimeter_bucket(perimeter_code)
        dates_to_purge = self.get_dates_to_purge(perimeter_code, treatment_date)
        for date_directory in dates_to_purge:
            path_to_delete = "%s/%s" % (bucket, date_directory)
            log_message(perimeter_code, "purging old treatment directory %s" % path_to_delete)
            gcs_utils.delete_file(path_to_delete)

    def get_token(self, perimeter_code, current_timestamp, max_execution_time):
        bucket = self.get_perimeter_bucket(perimeter_code)
        token_path = "%s/%s/%s" % (bucket, TreatmentFileManager.TOKEN_PREFIX, perimeter_code)
        if gcs_utils.exists_file_gcs(token_path):
            old_timestamp = float(gcs_utils.read_gcs_file_content(token_path))
            if (current_timestamp - old_timestamp) < max_execution_time:
                log_message(perimeter_code, "token file %s" % token_path)
                return False  # currently locked
            else:
                log_message(perimeter_code, "lock %s is ignored because older than %s s"
                            % (token_path, max_execution_time))
        # taking a new lock
        gcs_utils.create_file_with_content(bucket.strip("gs://"),
                                           "%s/%s" % (TreatmentFileManager.TOKEN_PREFIX, perimeter_code),
                                           "%s" % current_timestamp)
        return True

    def release_token(self, perimeter_code):
        bucket = self.get_perimeter_bucket(perimeter_code)
        token_path = "%s/%s/%s" % (bucket, TreatmentFileManager.TOKEN_PREFIX, perimeter_code)
        gcs_utils.delete_file(token_path)
        log_message(perimeter_code, "releasing lock %s" % token_path)
