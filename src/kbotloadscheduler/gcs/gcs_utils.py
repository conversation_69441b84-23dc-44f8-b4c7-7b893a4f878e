import io
import logging
from typing import Iterator
import base64

from google.cloud import storage
from google.api_core import exceptions as gcs_exceptions
import json
import re

# Le logger est spécifique à cette fonction pour ne pas interférer avec le reste.
stream_logger = logging.getLogger(__name__ + '.stream_uploader')

def get_gcs_blob(gcs_uri):
    """Return the blob corresponding to gcs_uri, None if it doesn't exist

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name

    Returns:
        Blob: the blob corresponding to gcs_uri, None if it doesn't exist
    """
    bucket_name, object_name = get_bucket_and_object_name(gcs_uri)
    client = storage.Client()
    bucket = client.get_bucket(bucket_name)
    return bucket.get_blob(object_name)


def read_gcs_file_content(gcs_uri):
    """Return the content of blob corresponding to gcs_uri, None if it doesn't exist

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name

    Returns:
        str: the content of blob corresponding to gcs_uri, None if it doesn't exist
    """
    blob = get_gcs_blob(gcs_uri)
    if blob is None:
        return ''
    content = blob.download_as_bytes()
    return content.decode()


def get_json_from(gcs_uri, default_json='{}'):
    """Return the dict associated to the json content in the blob corresponding to gcs_uri, None if it doesn't exist

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name

        default_json (str): default json if content is empty

    Returns:
        dict: the dict associated to the json content in the blob corresponding to gcs_uri, None if it doesn't exist
    """
    json_content = read_gcs_file_content(gcs_uri)
    if json_content != '':
        return json.loads(json_content)
    return json.loads(default_json)


def get_bucket_and_object_name(gcs_uri):
    """From gs://bucket_name/object_name returns bucket_name and object_name.

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name

    Returns:
        str, str: return bucket_name, object_name retrieved from gcs_uri
    """
    bucket = ""
    object_name = ""
    matches_with_object = re.match("gs://(.*?)/(.*)", gcs_uri)
    if matches_with_object:
        bucket, object_name = matches_with_object.groups()
    else:
        matches_without_object = re.match("gs://(.*)", gcs_uri)
        if matches_without_object:
            bucket = matches_without_object.groups()[0]
    return bucket, object_name


def list_gcs_blob(gcs_uri):
    """List all blobs under gcs_uri

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name

    Returns:
        list: list of objects under the given gcs_uri
    """
    bucket_name, object_name = get_bucket_and_object_name(gcs_uri)
    client = storage.Client()
    return list(client.list_blobs(bucket_name, prefix=object_name))


def list_gcs_blob_name(gcs_uri, blobs_name=None):
    """List all blobs under gcs_uri

    Args:
        gcs_uri (str): full gcs uri like gs://bucket_name/object_name
        blobs_name (str[]): list of already found blobs
    Returns:
        list: list of objects undr the given gcs_uri
    """
    if blobs_name is None:
        blobs_name = []
    bucket_name, object_name = get_bucket_and_object_name(gcs_uri)
    client = storage.Client()
    blobs = client.list_blobs(bucket_name, prefix=object_name)
    for blob in blobs:
        blobs_name.append(blob.name)

    return blobs_name


def list_gcs_direct_child_blob(bucket_name, prefix=''):
    """List all blobs directly under gcs_uri

    Args:
        bucket_name (str): name of the bucket
        prefix (str): prefix du chemin vers les blob recherchés

    Returns:
        list: list of objects under the given gcs_uri
    """
    client = storage.Client()
    blobs = client.list_blobs(bucket_or_name=bucket_name, prefix=prefix, delimiter="/", max_results=1)
    next(blobs, ...)  # Force list_blobs to make the api call (lazy loading)
    return list(blobs.prefixes)


def exists_folder_in_bucket_gcs(gcs_uri):
    """
    Check if uri folder exists.

    Args:
        gcs_uri: full gcs uri like gs://bucket_name/object_name

    Returns:
        Boolean: if folder exists return True otherwise False
    """
    uri_blobs = list_gcs_blob(gcs_uri)
    return True if len(uri_blobs) > 0 else False


def exists_file_gcs(gcs_file_path):
    """Check if file exist in gcs or not.

    Args:
        gcs_file_path (str): full gcs file path (with gs)

    Returns:
        boolean: return True if the file exists
    """
    if not gcs_file_path.startswith("gs://"):
        return False
    client = storage.Client()
    blob = get_gcs_blob(gcs_file_path)
    return False if blob is None else blob.exists(client)


def create_empty_file(bucket_name, file_path):
    """
    This function creates an empty file in the specified location within the specified bucket using GCS.

    Args:
    bucket_name: name of the bucket where the file should be created
    file_path: the path of the file to be created

    Returns:
    None
    """
    # Create a client object
    client = storage.Client()
    # Get the bucket you want to upload the file to
    bucket = client.get_bucket(bucket_name)
    # Create an empty file
    blob = storage.Blob(bucket=bucket, name=file_path)
    blob.upload_from_string('')


def create_file_with_content(bucket_name, file_path, content):
    """
    This function creates a file with specified content in the specified location within the specified bucket using GCS.

    Args:
    bucket_name: name of the bucket where the file should be created
    file_path: the path of the file to be created
    content: the content of the file

    Returns:
    None
    """
    print(f"🔧 GCS create_file_with_content() START")
    print(f"   🪣 bucket_name: {bucket_name}")
    print(f"   📄 file_path: {file_path}")
    print(f"   📊 content size: {len(content)} chars")
    print(f"   🌐 Full GCS URI: gs://{bucket_name}/{file_path}")

    # Validate bucket name before attempting to create client
    if not bucket_name or not bucket_name.strip():
        raise ValueError(f"Invalid bucket name: '{bucket_name}'. Bucket name cannot be empty.")

    print(f"   🚀 Creating GCS client...")
    # Create a client object
    client = storage.Client()
    print(f"   ✅ GCS client created (project: {getattr(client, 'project', 'unknown')})")

    print(f"   🪣 Getting bucket: {bucket_name}")
    # Get the bucket you want to upload the file to
    bucket = client.get_bucket(bucket_name)
    print(f"   ✅ Bucket obtained: {bucket.name}")

    print(f"   📄 Creating blob: {file_path}")
    # Create an empty file
    blob = storage.Blob(bucket=bucket, name=file_path)

    print(f"   📤 Uploading content ({len(content)} chars)...")
    blob.upload_from_string(content)
    print(f"   ✅ Content uploaded successfully!")
    print(f"   🌐 File available at: gs://{bucket_name}/{file_path}")
    print(f"🔧 GCS create_file_with_content() END (REAL)")


def create_file_with_bytes_content(gcs_file_path, bytes_content):
    """
    This function creates a file with specified content as bytes
    in the specified location within the specified bucket using GCS.
    Usefull to write binary data (for pdf for example)

    Args:
    gcs_file_path: the full GCS path, e.g., "gs://my-bucket/folder/my-file.pdf"
    bytes_content: the content of the file

    Returns:
    None
    """
    # Extract bucket name and blob name from the GCS path
    bucket_name, blob_name = get_bucket_and_object_name(gcs_file_path)

    # Create a client object
    client = storage.Client()
    # Get the bucket you want to upload the file to
    bucket = client.get_bucket(bucket_name)
    # Create an empty file
    blob = storage.Blob(bucket=bucket, name=blob_name)
    with blob.open("wb") as f:
        f.write(bytes_content)


def create_file_from_stream(destination_path: str, content_iterator: Iterator[bytes]) -> int:
    """
    Uploads content from a byte iterator to Google Cloud Storage in a streaming fashion.

    This function avoids loading the entire content into memory, making it suitable
    for large files.

    Args:
        destination_path (str): The full GCS path, e.g., "gs://my-bucket/folder/my-file.pdf".
        content_iterator (Iterator[bytes]): An iterator that yields chunks of bytes.

    Returns:
        int: The total number of bytes written to GCS.

    Raises:
        ValueError: If the destination path is not in the correct 'gs://' format.
        google.api_core.exceptions.GoogleAPICallError: For issues during the GCS upload.
    """
    stream_logger.debug(f"Initiating stream upload to {destination_path}")
    try:
        # Reuse the existing function to maintain consistency.
        bucket_name, blob_name = get_bucket_and_object_name(destination_path)

        # Create a new client instance, following the pattern of other functions in this file.
        client = storage.Client()

        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)

        total_bytes_written = 0

        # We use an in-memory buffer that acts as a file-like object
        # for the upload_from_file method, which handles streaming efficiently.
        with io.BytesIO() as stream_buffer:
            for chunk in content_iterator:
                if chunk:  # Ensure we don't write empty chunks.
                    bytes_written = stream_buffer.write(chunk)
                    total_bytes_written += bytes_written

            # Seek back to the start of the buffer before beginning the upload.
            stream_buffer.seek(0)

            stream_logger.debug(
                f"Starting streaming upload of {total_bytes_written} bytes to gs://{bucket_name}/{blob_name}"
            )

            # upload_from_file handles sending the data stream to GCS.
            blob.upload_from_file(stream_buffer, content_type='application/octet-stream')

        stream_logger.info(
            f"Successfully streamed {total_bytes_written} bytes to {destination_path}"
        )
        return total_bytes_written

    except gcs_exceptions.GoogleAPICallError as e:
        stream_logger.error(f"GCS API error during streaming upload to {destination_path}: {e}", exc_info=True)
        # Re-raise the exception so the caller knows the operation failed.
        raise
    except Exception as e:
        stream_logger.error(f"An unexpected error occurred during streaming upload to {destination_path}: {e}", exc_info=True)
        raise

def generate_success_path_from_path_or_uri(path):
    """
    From uri or path remove the filename and add _SUCCESS
    Args:
        path: uri or output path file where

    Returns:
        success_path
    """
    return "/".join(path.split("/")[:-1]) + "/_SUCCESS"


def get_gcs_blob_size(gcs_uri):
    """Return the size of the blob(s) corresponding to gcs_uri, None if it doesn't exist

    Args:
        gcs_uri (str): gcs uri like gs://bucket_name/object_name or a prefix like gs://bucket_name/directory_name

    Returns:
        Size: the size of the blob(s) corresponding to gcs_uri, None if it doesn't exist
    """
    size = 0
    blobs = list_gcs_blob(gcs_uri)
    if len(blobs) == 0:
        return None
    for blob in blobs:
        size += blob.size
    return size


def find_last_file(gcs_uri, file_name):
    """
        Finds the last file in multiple date dir with the specified name in the provided GCS URI.

        Args:
            gcs_uri (str): The GCS URI of the directory where the files are located.
            file_name (str): The name of the file to search for.

        Returns:
            tuple: A tuple containing the name and blob of the last file found,
            or (None, None) if no files match the criteria.
    """
    if not gcs_uri:
        raise ValueError("The 'gcs_uri' parameter cannot be empty.")

    if not file_name:
        raise ValueError("The 'file_name' parameter cannot be empty.")

    # List all blobs in the bucket matching the directory prefix
    blobs = list_gcs_blob(gcs_uri)

    # Filter the blobs to find the ones with the desired filename
    blobs_filtered = [blob for blob in blobs if blob.name.endswith(file_name)]

    # Sort the blobs by name in descending order
    sorted_blobs = sorted(blobs_filtered, key=lambda x: x.name, reverse=True)

    # Return the last file's name and blob
    if sorted_blobs:
        last_file_blob = sorted_blobs[0]
        return last_file_blob.name, last_file_blob
    else:
        return None, None


def copy_file_to_destination(source_blob, gcs_destination_uri):
    """
        Copies a file from a source GCS blob to a destination gcs uri.

        Args:
            source_blob (Blob) : The source GCS blob to copy.
            gcs_destination_uri (str): The destination GCS bucket and directory in the format
            'gs://bucket_name/directory/object_name'.

        Returns:
            copy_blob (Blob) or None : The copied source blob
    """
    if source_blob is None:
        return None

    # Create a client instance
    storage_client = storage.Client()

    bucket_name, object_name = get_bucket_and_object_name(gcs_destination_uri)

    # Get the destination bucket
    destination_bucket = storage_client.get_bucket(bucket_name)

    # Check if the destination blob already exists
    destination_blob = destination_bucket.get_blob(object_name)
    if destination_blob:
        print(f"File '{object_name}' already exists in the destination directory.")
        return None
    else:
        # Copy the source blob to the destination bucket and directory
        blob_copy = source_blob.bucket.copy_blob(source_blob, destination_bucket, object_name)
        print(
            "Blob {} in bucket {} copied to blob {} in bucket {}.".format(
                source_blob.name,
                source_blob.bucket.name,
                blob_copy.name,
                destination_bucket.name))
        return blob_copy


def move_file_to_destination(source_blob, gcs_destination_uri):
    """
        Moves a file from a source GCS blob to a destination gcs uri.

        Args:
            source_blob (Blob) : The source GCS blob to copy.
            gcs_destination_uri (str): The destination GCS bucket and directory in the format
            'gs://bucket_name/directory/object_name'.

        Returns:
            copy_blob (Blob) or None : The copied source blob
    """

    copied_blob = copy_file_to_destination(source_blob, gcs_destination_uri)
    if copied_blob is None:
        return None
    source_blob.delete()
    return copied_blob


def flag_file_to_dict(gcs_uri, separator='='):
    """Get the dict associated to the flags in the gcs_uri blob content, None if absent or empty

    Args:
        gcs_uri (str) : full gcs uri like gs://bucket_name/object_name
        separator (str) : separator using in object to separate flag from its value
                        default_value is '='

    Returns:
        dict: the dict associated to the flags names and values
    """

    # read config file
    config = read_gcs_file_content(gcs_uri)
    if config is None:
        return None
    # delete comments
    config = re.sub(r'(?m)^ *#.*\n?', '', config.strip())
    if config == '':
        return None

    kv = [(line.split(separator)[0], line.split(separator)[1]) for line in config.split('\n')]
    return dict(kv)


def delete_file(gcs_uri):
    """
    Delete all files under the gcs_uri, Nothing if no file exist.

    Args:
        gcs_uri (str): gcs uri like gs://bucket_name/object_name or a prefix like gs://bucket_name/directory_name

    Returns:
        None

    """
    blobs = list_gcs_blob(gcs_uri)
    if len(blobs) == 0:
        return None
    for blob in blobs:
        blob.delete()


def read_gcs_file_and_encode_base64(gcs_uri):
    """
    Read the content of a gcs file and returns the content of the file encoded in base64.

    Args:
        gcs_uri (str): The GCS URI of the directory where the files are located.

    Returns:
        Content of the file encoded in base64.
    """
    file_content = read_gcs_file_content(gcs_uri)
    return base64.b64encode(file_content.encode()).decode()


TIDY_LIST = [
    ' ', '&', '#', '"', "'", '`', '°', '+', '*',
    '{', '[', '(', '|', ')', ']', '}',
    '\\', '\t', '\n', '\r',
    '§', '%', '^', '̈́"', '~'
]
TIDY_GENERAL_DICT = {char: '-' for char in TIDY_LIST}
TIDY_SPECIFIC_DICT = {
    'ç': 'c', '@': 'A', '$': 'S', '£': 'L', '€': 'E',
    '!': '.', '?': '.', ';': '.', ':': '.', ',': '.',
    'µ': 'm', 'ù': 'u', '/': '_'
}
TIDY_DICT = {**TIDY_GENERAL_DICT, **TIDY_SPECIFIC_DICT}


def tidy_file_name(file_name):
    return ''.join([TIDY_DICT.get(char, char) for char in file_name])
