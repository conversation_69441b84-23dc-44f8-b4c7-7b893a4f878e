# Documentation du Chargeur SharePoint

## Vue d'ensemble

Le **SharePoint Loader** est un composant du système Kbot Load Scheduler qui permet de récupérer et charger des documents depuis des sites SharePoint vers un stockage cloud (Google Cloud Storage). Il fait partie du module de chargement de données et implémente l'interface `AbstractLoader`, s'intégrant dans un écosystème RAG (Retrieval-Augmented Generation).

## Architecture

### Classes principales

1.  **`SharepointLoader`** : Classe principale qui orchestrie le chargement des documents.
2.  **`SharepointClient`** : Client pour interagir avec l'API REST SharePoint.
3.  **`SharepointCredentials`** : Gestionnaire d'authentification avec SharePoint via certificat client.

## Fonctionnalités principales

### 1. Découverte de documents (`get_document_list`)

Le chargeur parcourt récursivement une structure de dossiers SharePoint pour identifier tous les documents disponibles.

**Processus :**
- Se connecte au site SharePoint spécifié.
- Navigue vers le répertoire relatif configuré (`relative_directory`).
- Parcourt récursivement tous les sous-dossiers en appelant l'API SharePoint.
- Collecte et retourne les métadonnées de tous les fichiers trouvés.

**Données d'entrée :**
- `source` (`SourceBean`) : Objet contenant la configuration de la source SharePoint.

**Données de sortie :**
- `List[DocumentBean]` : Liste des documents trouvés, chaque `DocumentBean` contient :
    - `id` : Identifiant unique au format `{domain_code}|{source_code}|{file_id}`.
    - `name` : Chemin relatif du fichier dans SharePoint (`ServerRelativeUrl`).
    - `path` : URL complète d'accès au fichier.
    - `modification_time` : Date de dernière modification (objet `datetime`).

### 2. Téléchargement de documents (`get_document`)

Le chargeur télécharge des documents individuels depuis SharePoint et les stocke dans un bucket GCS.

**Processus :**
1.  Authentification auprès de SharePoint.
2.  Récupération des métadonnées du fichier via son identifiant.
3.  Téléchargement du contenu binaire du fichier.
4.  Stockage dans GCS avec un nom de fichier nettoyé.
5.  Retourne un dictionnaire de métadonnées enrichies.

**Données d'entrée :**
- `source` (`SourceBean`) : Configuration de la source SharePoint.
- `document` (`DocumentBean`) : Document à télécharger.
- `output_path` (`str`) : Chemin de destination dans GCS (ex: `"gs://bucket/documents/"`).

**Données de sortie :**
- `Dict[str, Any]` : Dictionnaire de métadonnées contenant :
    - `document_id` : Identifiant unique du document.
    - `document_name` : Nom/chemin original du document.
    - `source_path` : URL source dans SharePoint.
    - `location` : Chemin de stockage final dans GCS.
    - `creationTime` : Date de création du fichier.
    - `modificationTime` : Date de dernière modification.

### 3. Modes d'identification des documents

Le système supporte deux modes pour identifier les fichiers, configurables via la variable d'environnement `SHAREPOINT_IN_DOC_ID_USE_PROPERTY`.

#### Mode `UniqueId` (recommandé)
- Utilise l'identifiant unique (GUID) fourni par SharePoint.
- **Avantage :** Stable et ne change pas si un fichier est renommé ou déplacé.
- Format d'ID : `domaine|source|guid-unique`

#### Mode `ServerRelativeUrl`
- Utilise le chemin relatif du fichier sur le serveur.
- **Inconvénient :** Moins stable, car il change si le fichier est déplacé ou renommé.
- Format d'ID : `domaine|source|/sites/site/chemin/fichier.ext`

## Configuration

### Variables d'environnement

```bash
# URL du service SharePoint
URL_SERVICE_SHAREPOINT=https://votreorganisation.sharepoint.com

# Mode d'identification des documents
SHAREPOINT_IN_DOC_ID_USE_PROPERTY=UniqueId  # ou ServerRelativeUrl
```

### Configuration de la source (`SourceBean`)

La configuration spécifique à SharePoint est fournie via un champ JSON.

```python
# Exemple de configuration dans un objet SourceBean
source_config = {
    "site_name": "NomDuSiteSharePoint",
    "relative_directory": "Documents partages/Dossier/Cible"
}
```

### Configuration d'authentification

Le système utilise l'authentification par certificat client. Les secrets sont gérés par un gestionnaire externe.

```json
{
    "tenant_id": "id-du-tenant-azure",
    "client_id": "id-de-l-application",
    "certificate_thumbprint": "empreinte-du-certificat",
    "password": "mot-de-passe-certificat"
}
```

## Exemples d'utilisation

### Exemple 1 : Lister les documents

```python
# Configuration
loader = SharepointLoader(config_with_secret)
source = SourceBean(
    domain_code="mondomaine",
    code="masource",
    configuration=json.dumps({
        "site_name": "EquipeIA",
        "relative_directory": "Documents partages/Knowledge Bot"
    })
)

# Récupération de la liste des documents
documents = loader.get_document_list(source)
print(f"Trouvé {len(documents)} documents.")

for doc in documents[:5]:
    print(f"- {doc.name} (ID: {doc.id})")
```

### Exemple 2 : Télécharger un document

```python
# Document à télécharger (obtenu depuis la liste précédente, généré par/document/compare/{perimeter_code}
document = DocumentBean(
    id="domaine|source|af7b286c-027b-4a20-b9b7-02b752b87b53",
    name="/sites/EquipeIA/Documents partages/rapport.pdf",
    path="...",
    modification_time=datetime.now()
)

# Téléchargement
metadata = loader.get_document(
    source=source,
    document=document,
    output_path="gs://bucket/documents/"
)

print(f"Document sauvegardé : {metadata['location']}")
print(f"Date de création : {metadata['creationTime']}")
print(f"Date de modification : {metadata['modificationTime']}")
```

## Gestion des erreurs

- **`SharepointException`** : Le `SharepointClient` lève cette exception pour les erreurs d'API (401, 403, 404, etc.).
- **`LoaderException`** : Le `SharepointLoader` lève cette exception pour les erreurs de plus haut niveau, en encapsulant souvent une `SharepointException`.

## Bonnes pratiques implémentées

- **Structure des identifiants** : Le format `{domain_code}|{source_code}|{file_identifier}` garantit l'unicité des documents à travers tout le système.
- **Nettoyage des noms de fichiers** : La fonction `gcs_utils.tidy_file_name` est utilisée pour normaliser les noms de fichiers avant de les sauvegarder sur GCS, assurant la compatibilité avec le système de stockage.

## Contexte d'intégration et Workflow typique

Le `SharepointLoader` est conçu pour fonctionner au sein d'un workflow d'orchestration plus large, piloté par **Cloud Scheduler**. Ses méthodes ne sont généralement pas appelées directement, mais en réponse à des tâches définies dans des fichiers sur GCS.

```mermaid
graph TD
    A["Phase 1: Découverte<br><b>loader.get_document_list(source)</b>"] --> B["Un orchestrateur écrit la liste<br>dans <b>list.json</b> sur GCS"]
    B --> C["Phase 2: Comparaison<br>Un service externe (DocumentService)<br>compare <b>list.json</b> avec l'index existant"]
    C --> D["L'orchestrateur crée des fichiers de tâches<br><b>getdoc.json</b> pour chaque nouveau document"]
    D --> E["Phase 3: Traitement Individuel<br><b>loader.get_document(source, document)</b>"]
    E --> F["Le contenu est téléchargé sur GCS"]
    F --> G["Un fichier <b>.metadata.json</b> est écrit"]
    G --> H["Phase 4: Indexation<br>Un service d'embedding traite<br>les fichiers <b>.metadata.json</b>"]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#fff9c4
    style H fill:#dcedc8
```

Cette architecture découplée offre plusieurs avantages :
1.  **Résilience** : Chaque étape est indépendante et peut être rejouée en cas d'erreur.
2.  **Scalabilité** : Le traitement des documents (`get_document`) peut être parallélisé.
3.  **Auditabilité** : GCS conserve une trace de chaque étape du processus.

## Limitations actuelles

1.  **Lecture seule** : Le loader ne modifie pas les documents sur SharePoint.
2.  **Authentification unique** : Seule l'authentification par certificat client est supportée.
3.  **Pas de versioning** : Seule la version la plus récente d'un document est récupérée.
4.  **Gestion de la mémoire** : La méthode `get_document_list` charge la liste complète des fichiers d'un répertoire et de ses sous-répertoires en mémoire. Pour des sites SharePoint contenant des dizaines de milliers de fichiers, cela peut entraîner une consommation mémoire élevée et potentiellement des erreurs (`MemoryError`), notamment dans des environnements contraints comme Cloud Run.

## Évolutions prévues

1.  **Pagination et Traitement par Lots** : Implémenter une méthode de découverte paginée pour traiter les très grands volumes de documents sans saturer la mémoire.
2.  **Métadonnées de Relation** : Extraire des métadonnées basées sur la structure des dossiers (chemin, profondeur, dossier parent) pour enrichir le contexte des documents.
3.  **Filtrage en Amont** : Ajouter des options de configuration pour filtrer les fichiers par extension, taille ou date de modification directement au niveau de l'API SharePoint pour réduire la charge.
4.  **Cache Intelligent** : Mettre en place un mécanisme pour éviter de re-télécharger des documents qui n'ont pas changé, en se basant sur la date de modification.
