import os
from ..abstract_loader import AbstractLoader
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.loader.basic.preprocess import preprocess_sheet, parse_datetime
from kbotloadscheduler.loader.basic.basic_client import ProcedureSheetClient
from kbotloadscheduler.loader.basic.auth import AuthProvider
from kbotloadscheduler.gcs.gcs_utils import create_file_with_content
from kbotloadscheduler.bean.beans import DocumentBean, Metadata
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from tqdm import tqdm


class BasicLoader(AbstractLoader):
    """Loader pour les fiches basic"""

    def __init__(self, config_with_secret: ConfigWithSecret, auth_provider: AuthProvider):
        super().__init__("basic")
        url_service = os.getenv("URL_SERVICE_BASIC")
        ca_certificate = ""
        timeout = int(os.getenv("TIMEOUT_BASIC", 120))
        self.env = os.getenv("ENV_GCP")
        if not self.env:
            self.env = os.getenv("ENV")

        self.ps_client = ProcedureSheetClient(
            url_service,
            auth_provider,
            ca_certificate,
            timeout,
            self.env
        )

    def get_document_list(self, source: SourceBean):
        content_ids_from_source_str = source.get_expected_conf("content_ids")
        if content_ids_from_source_str:
            content_ids_from_source = content_ids_from_source_str.split(",")
        else:
            content_ids_from_source = []
        content_ids = self.ps_client.request_contents_by_status_ba()
        content_ids = list(set(content_ids + content_ids_from_source))
        print(content_ids)
        domain = source.domain_code
        source_code = source.code
        if content_ids:
            infos = {}
            for cid in tqdm(content_ids, desc="Getting Specific Sheet Info"):
                sheet_info = self.ps_client.request_sheet_info_by_content_id(cid).json()
                if sheet_info:
                    infos[cid] = sheet_info[-1]
            result = [
                DocumentBean(id=f"{domain}/{source_code}/cid_{cid}",
                             path=f"https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/{sheet['id']}",
                             name=sheet["content"]["title"],
                             modification_time=parse_datetime(sheet["content"]["privateModificationDate"]),
                             ) for cid, sheet in infos.items()
            ]
            return result
        else:
            infos = self.ps_client.request_sheet_infos(10_000).json()
            result = [
                DocumentBean(
                    id=f"{domain}/{source_code}/cid_{sheet['content']['id']}",
                    path=f"https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/{sheet['id']}",
                    name=sheet["content"]["title"],
                    modification_time=parse_datetime(sheet["content"]["privateModificationDate"]),
                ) for sheet in infos
            ]
            return result

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        metadata = {}

        content_id = document.id.split("cid_")[-1]
        sheet = self.ps_client.get_sheets_from_content_ids([content_id])[0]

        # if sheet["cases"] or sheet["tabs"]:
        content, _metadata = preprocess_sheet(sheet, self.env)
        # else:   # Todo: ask about edge case
        #     content = ""
        #     _metadata = {}

        splits = output_path.split("gs://")[-1].split("/")
        path = "/".join(splits[1:])
        bucket = splits[0]
        file_path = os.path.join(path, f"cid_{content_id}.basic")
        create_file_with_content(bucket, file_path, content)

        gcs_path = os.path.join("gs://" + bucket, file_path)

        metadata[Metadata.DOCUMENT_ID] = document.id
        metadata[Metadata.DOCUMENT_NAME] = document.name
        metadata[Metadata.LOCATION] = gcs_path
        metadata[Metadata.DOMAIN_CODE] = source.domain_code
        metadata[Metadata.SOURCE_CODE] = source.code

        metadata = {**metadata, **_metadata}

        return metadata
