from abc import ABC, abstractmethod
import requests
from requests.auth import HTTPBasicAuth

APPLICATION_JSON = "application/json"


class AuthProvider(ABC):
    @abstractmethod
    def get_headers(self) -> dict:
        pass


class OkapiAuthProvider(AuthProvider):
    def __init__(self, okapi_url, client_id, client_secret, service_scope, ca_certificate):
        self.okapi_url = okapi_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.service_scope = service_scope
        self.ca_certificate = ca_certificate

    def get_headers(self) -> dict:
        r = requests.post(
            self.okapi_url,
            auth=HTTPBasicAuth(self.client_id, self.client_secret),
            timeout=300,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": APPLICATION_JSON,
            },
            data={"grant_type": "client_credentials", "scope": self.service_scope},
            verify=self.ca_certificate,
        )
        r.raise_for_status()
        token = r.json()["access_token"]
        
        return {
            "Content-Type": APPLICATION_JSON,
            "Accept": APPLICATION_JSON,
            "Accept-Language": "fr",
            "Authorization": f"Bearer {token}",
        }


class MockAuthProvider(AuthProvider):
    def __init__(self, env):
        self.env = env

    def get_headers(self) -> dict:
        print(f"Using mock token for {self.env} environment.")
        return {
            "Content-Type": APPLICATION_JSON,
            "Accept": APPLICATION_JSON,
            "Accept-Language": "fr",
            "Authorization": "Bearer mock-token-for-testing",
        }