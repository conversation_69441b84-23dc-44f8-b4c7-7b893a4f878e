""""
Confluence Credentials Management

This module manages authentication credentials for the Confluence API.
"""

import base64
import os
from dataclasses import dataclass

# Note: cloud=True is for Confluence Cloud, cloud=False is for Server/Data Center.

@dataclass(frozen=True)
class ConfluenceCredentials:
    """
    Credentials for Confluence API authentication.

    This data class stores the information required to connect to a Confluence
    instance, whether Cloud or Server/Data Center.

    Attributes:
        url: The URL of the Confluence instance.
        username: The username (usually an email address for Confluence Cloud).
        api_token: The API token to use with the username.
        pat_token: The Personal Access Token (PAT). Takes precedence over
                   username/API token authentication.
        cloud: True for Confluence Cloud, False for Server/Data Center.
    """

    url: str
    username: str | None = None
    api_token: str | None = None
    pat_token: str | None = None
    cloud: bool = True

    @classmethod
    def from_env(cls) -> "ConfluenceCredentials":
        """
        Create a credentials object from environment variables.

        Expected environment variables:
        - CONFLUENCE_URL
        - CONFLUENCE_USERNAME (optional)
        - CONFLUENCE_API_TOKEN (optional)
        - CONfluence_PAT_TOKEN (optional)
        - CONFLUENCE_CLOUD: Interprets 'true' (case-insensitive) as True.
          If unset or any other value, defaults to False.

        Returns:
            A new `ConfluenceCredentials` instance.

        Raises:
            ValueError: If the CONFLUENCE_URL is missing or empty.
        """
        url = os.getenv("CONFLUENCE_URL", "").strip()
        if not url:
            raise ValueError("The CONFLUENCE_URL environment variable is missing or empty.")

        return cls(
            url=url,
            username=os.getenv("CONFLUENCE_USERNAME"),
            api_token=os.getenv("CONFLUENCE_API_TOKEN"),
            pat_token=os.getenv("CONFLUENCE_PAT_TOKEN"),
            cloud=os.getenv("CONFLUENCE_CLOUD", "").lower() == "true",
        )

    @classmethod
    def from_secret_dict(cls, secret_dict: dict) -> "ConfluenceCredentials":
        """
        Create a credentials object from a dictionary, e.g., from a secret manager.

        Args:
            secret_dict: A dictionary containing 'confluence_url', 'cloud',
                         and authentication secrets.

        Returns:
            A new `ConfluenceCredentials` instance.

        Raises:
            ValueError: If 'confluence_url' is missing or empty, or if 'cloud'
                        is missing or not a boolean.
        """
        url = secret_dict.get("confluence_url")
        if not url or not url.strip():
            raise ValueError("The 'confluence_url' key is missing or empty in the secret dictionary.")

        cloud_status = secret_dict.get("cloud")
        if not isinstance(cloud_status, bool):
            raise ValueError("The 'cloud' key is mandatory and must be a boolean (true/false) in the secret dictionary.")

        return cls(
            url=url.strip(),
            username=secret_dict.get("username"),
            api_token=secret_dict.get("api_token"),
            pat_token=secret_dict.get("pat_token"),
            cloud=cloud_status,
        )

    def is_valid(self) -> bool:
        """
        Check if the credentials are sufficient for authentication.

        Credentials are considered valid if the URL is present and at least one
        authentication method is complete (either PAT or username/API token).

        Returns:
            True if the credentials are valid, otherwise False.
        """
        has_url = bool(self.url and self.url.strip())
        has_pat = bool(self.pat_token)
        has_user_api = bool(self.username and self.api_token)

        return has_url and (has_pat or has_user_api)

    def get_auth_headers(self) -> dict:
        """
        Generate authentication headers for API requests.

        Gives priority to the PAT token (Bearer) if available, otherwise uses
        Basic authentication with username and API token.

        Returns:
            A dictionary with the `Authorization` header, or an empty dict if
            no authentication information is available.
        """
        if self.pat_token:
            return {"Authorization": f"Bearer {self.pat_token}"}
        if self.username and self.api_token:
            credentials = f"{self.username}:{self.api_token}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            return {"Authorization": f"Basic {encoded_credentials}"}
        return {}

    def __repr__(self) -> str:
        """
        Return a string representation of the object, masking sensitive tokens.

        Returns:
            A string representing the `ConfluenceCredentials` object.
        """
        masked_pat_token = "***" if self.pat_token else None
        masked_api_token = "***" if self.api_token else None

        return (
            f"ConfluenceCredentials("
            f"url='{self.url}', "
            f"username={self.username!r}, "
            f"api_token={masked_api_token!r}, "
            f"pat_token={masked_pat_token!r}, "
            f"cloud={self.cloud})"
        )
