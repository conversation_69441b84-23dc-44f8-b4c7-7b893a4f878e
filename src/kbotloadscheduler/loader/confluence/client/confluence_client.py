import logging
import time
import json
import re
import socket
import ssl
import xml.etree.ElementTree as ET
from contextlib import contextmanager
from typing import Any, Optional, List, Dict, Iterator
from functools import lru_cache, cached_property
from dataclasses import dataclass
from urllib.parse import urlparse
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed

import requests
from atlassian import Confluence

from ..exceptions import (
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    ConfluenceNotFoundError,
    ConfluenceTimeoutError,
)
from ..config.schema import ConfluenceConfig
from .confluence_credentials import ConfluenceCredentials
from ..utils.retry_factory import create_api_retryer, create_search_retryer

logger = logging.getLogger(__name__)

__all__ = ["ConfluenceClient", "AttachmentMeta"]

# ------------------------------------------------------------------
# 1. Security & Debugging Helpers (UNCHANGED)
# ------------------------------------------------------------------

def _redact_headers(headers: dict) -> dict:
    """Return headers with likely secrets masked."""
    secret_keys = {
        'authorization', 'auth', 'token', 'key', 'secret', 'password',
        'credential', 'bearer', 'basic', 'api-key', 'x-api-key',
        'x-atlassian-token', 'x-auth', 'cookie', 'session'
    }

    def _looks_secret(key: str, value: str) -> bool:
        """Check if a key or value appears to be a secret."""
        key = key.lower()
        if any(s in key for s in secret_keys):
            return True
        return len(value) > 20 and re.fullmatch(r'[\w+/=_-]+', value) is not None

    return {k: '***REDACTED***' if _looks_secret(k, str(v)) else v for k, v in headers.items()}


def _safe_json_preview(data: dict | list | str, max_bytes: int = 200) -> str:
    """Return a truncated JSON string representation for logging."""
    try:
        json_str = json.dumps(data, separators=(',', ':')) if isinstance(data, (dict, list)) else str(data)
        if len(json_str) > max_bytes:
            return json_str[:max_bytes] + "…[truncated]"
        return json_str
    except Exception:
        return f"<non-serializable {type(data).__name__}>"

# ------------------------------------------------------------------
# 2. Confluence Client
# ------------------------------------------------------------------

@dataclass(frozen=True)
class AttachmentMeta:
    """Metadata for a Confluence attachment."""
    id: str
    download_link: str


class ConfluenceClient:
    _USER_AGENT = "Mozilla/5.0 (compatible; KbotConfluenceClient/1.3)"

    # -------------------------------
    # Construction / teardown
    # -------------------------------
    def __init__(self, credentials: ConfluenceCredentials, config: ConfluenceConfig):
        if not credentials.is_valid():
            raise ConfluenceAuthenticationError("Invalid or incomplete credentials provided.")

        self.credentials = credentials
        self.config = config
        self.url = self.credentials.url.rstrip("/")
        self.verify_ssl = True
        self.timeout = self.config.file_processing.default_timeout
        self._session: Optional[requests.Session] = None
        self._confluence_instance: Optional[Confluence] = None

        self._executor = ThreadPoolExecutor(
            max_workers=self.config.performance.max_parallel_workers,
            thread_name_prefix='ConfluenceWorker'
        )

        self._configure_session()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        """Closes sessions, executor, and clears caches."""
        # --- MODIFIED: Shutdown executor ---
        self._executor.shutdown(wait=True)

        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Custom requests session closed.")
        if self._confluence_instance and hasattr(self._confluence_instance, '_session'):
            self._confluence_instance._session.close()
            logger.debug("Atlassian library main session closed.")
        self._fetch_attachment_meta_from_api.cache_clear()

    # -------------------------------
    # Internal setup
    # -------------------------------

    # --- MODIFIED: Lazy property for the main thread's client instance ---
    @cached_property
    def confluence(self) -> Confluence:
        """
        Lazily initializes and returns the main atlassian-python-api client.
        This instance should only be used by the main thread.
        """
        logger.debug("Initializing main thread Confluence client instance.")
        return self._create_thread_safe_client()

    def _create_thread_safe_client(self) -> Confluence:
        """
        Creates a new, isolated Confluence client instance.
        This is essential for thread-safe parallel operations, as each worker
        thread needs its own client and session.
        """
        auth_kwargs = {}
        if self.credentials.pat_token:
            auth_kwargs['token'] = self.credentials.pat_token
        elif self.credentials.username and self.credentials.api_token:
            auth_kwargs['username'] = self.credentials.username
            auth_kwargs['password'] = self.credentials.api_token
        else:
            # This should have been caught in __init__, but as a safeguard:
            raise ConfluenceAuthenticationError("No valid authentication method found in credentials.")

        return Confluence(
            url=self.url,
            timeout=self.timeout,
            verify_ssl=self.verify_ssl,
            cloud=self.credentials.cloud,
            **auth_kwargs
        )

    def _configure_session(self) -> None:
        """Configures the internal requests.Session for direct API calls."""
        self._session = requests.Session()
        self._session.verify = self.verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": self._USER_AGENT
        }
        headers.update(self.credentials.get_auth_headers())
        self._session.headers.update(headers)

    # (The following private HTTP methods are unchanged)
    # _safe_request_context, _make_authenticated_request, _collect_debug_snapshot
    # _enrich_debug_on_error, _wrap_exception, _check_dns_resolution
    @contextmanager
    def _safe_request_context(self, method: str, url: str, **kwargs):
        """A context manager for making safe, debuggable HTTP requests."""
        start = time.time()
        debug_info = self._collect_debug_snapshot(method, url, kwargs)
        kwargs.setdefault('timeout', self.timeout)

        resp = None
        try:
            resp = self._make_authenticated_request(method, url, **kwargs)
            resp.raise_for_status()
            yield resp
        except requests.exceptions.RequestException as exc:
            duration = time.time() - start
            debug_info.update(self._enrich_debug_on_error(exc, url, duration))
            raise self._wrap_exception(exc, url, debug_info) from exc
        finally:
            if resp:
                resp.close()

    def _make_authenticated_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Executes a request using the configured session."""
        if self._session is None:
            self._configure_session()
        return self._session.request(method, url, **kwargs)

    def _collect_debug_snapshot(self, method: str, url: str, kwargs: dict) -> dict[str, Any]:
        """Gathers pre-request information for debugging."""
        snap: dict[str, Any] = {
            'method': method, 'url': url, 'timeout': kwargs.get('timeout', self.timeout),
            'ssl_verify': self.verify_ssl, 'confluence_url': self.url,
        }
        if method.upper() in ('POST', 'PUT'):
            snap['request_body'] = _safe_json_preview(kwargs.get('json') or kwargs.get('data', ''))
        if self._session:
            snap['safe_headers'] = _redact_headers(dict(self._session.headers))
        return snap

    def _enrich_debug_on_error(self, exc: Exception, url: str, duration: float) -> dict[str, Any]:
        """Adds post-request failure details to the debug info."""
        details: dict[str, Any] = {"duration": round(duration, 2), "error_type": type(exc).__name__}
        if isinstance(exc, requests.exceptions.HTTPError) and exc.response is not None:
            resp = exc.response
            details.update(
                status_code=resp.status_code, reason=resp.reason,
                response_headers=_redact_headers(dict(resp.headers)),
            )
            if resp.content and len(resp.content) < 500:
                details["response_body"] = resp.text[:500]
        elif isinstance(exc, (requests.exceptions.Timeout, requests.exceptions.ConnectionError)):
            details["dns_ok"] = self._check_dns_resolution(url)
        elif isinstance(exc, requests.exceptions.SSLError):
            details["ssl_version"] = ssl.OPENSSL_VERSION
        return details

    def _wrap_exception(self, exc: Exception, url: str, debug: dict[str, Any]) -> ConfluenceClientException:
        """Wraps a requests exception in a custom Confluence exception."""
        summary = f"[{type(exc).__name__} for {url}]"
        if isinstance(exc, requests.exceptions.HTTPError) and exc.response is not None:
            return ConfluenceClientException.from_http_error(exc.response, message=summary, debug_details=debug)
        if isinstance(exc, requests.exceptions.Timeout):
            return ConfluenceTimeoutError(summary, debug_details=debug, resource=url)
        return ConfluenceClientException(summary, debug_details=debug, resource=url)

    @staticmethod
    def _check_dns_resolution(url: str) -> bool:
        """Checks if the hostname in a URL can be resolved."""
        try:
            hostname = urlparse(url).hostname
            if hostname:
                socket.gethostbyname(hostname)
                return True
        except (socket.gaierror, TypeError):
            return False
        return False

    # -------------------------------
    # Retries wrapper
    # -------------------------------
    def _call_with_retry(self, func, *args, **kw):
        """Invokes a function with a configured retry strategy."""
        retryer = create_api_retryer(self.config)
        return retryer(func, *args, **kw)

    # -------------------------------
    # Attachments
    # -------------------------------
    def _is_valid_attachment(self, attachment: dict[str, Any]) -> bool:
        """Checks if an attachment is a temporary or hidden file."""
        from ..config.helpers import is_temp_file
        title = attachment.get("title", "")
        return not (title.startswith((".", "~")) or is_temp_file(self.config, title))

    def _filter_current_attachments(self, attachments: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Filters a list of attachments to include only valid, current ones."""
        return [att for att in attachments if att.get("status") == "current" and self._is_valid_attachment(att)]

    def get_page_attachments_all_current(self, page_id: str, client: Optional[Confluence] = None) -> list[dict[str, Any]]:
        """
        Retrieves all current attachments for a given page, handling pagination.
        Accepts an optional client instance for thread-safe parallel execution.
        """
        try:
            confluence_instance = client or self.confluence
            all_attachments = []
            start, limit = 0, 50

            while True:
                response = create_search_retryer(self.config)(
                    lambda: confluence_instance.get_attachments_from_content(
                        page_id=page_id, start=start, limit=limit
                    )
                )
                results = response.get("results", [])
                all_attachments.extend(self._filter_current_attachments(results))

                if not results or len(results) < limit:
                    break
                start += len(results)

            return all_attachments
        except Exception as exc:
            logger.error(f"Error fetching attachments for page {page_id}: {exc}", exc_info=True)
            raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{page_id}/child/attachment", {})

    @contextmanager
    def _thread_safe_client(self):
        """Context manager for thread-safe client instances."""
        client = None
        try:
            client = self._create_thread_safe_client()
            yield client
        finally:
            if client and hasattr(client, '_session'):
                try:
                    client._session.close()
                except Exception as cleanup_error:
                    logger.warning(f"Error during client cleanup: {cleanup_error}")

    def _fetch_all_attachments_for_page_worker(self, page_id: str) -> Dict[str, Any]:
        """Improved worker using context manager for resource safety."""
        try:
            with self._thread_safe_client() as client:
                attachments = self.get_page_attachments_all_current(page_id, client=client)
                return {"page_id": page_id, "attachments": attachments, "error": None}
        except Exception as e:
            logger.error(f"Failed to fetch attachments for page {page_id} in worker thread: {e}", exc_info=True)
            return {"page_id": page_id, "attachments": [], "error": e}

    def get_all_attachments_for_pages(self, page_ids: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        if not page_ids:
            return {}

        results: Dict[str, List[Dict[str, Any]]] = {}
        future_to_page_id = {
            self._executor.submit(self._fetch_all_attachments_for_page_worker, page_id): page_id
            for page_id in page_ids
        }

        for future in as_completed(future_to_page_id):
            page_id = future_to_page_id[future]
            try:
                result = future.result()
                if result["error"]:
                    logger.warning("Skipping attachments for page %s: %s", page_id, result["error"])
                    results[page_id] = []
                else:
                    results[page_id] = result["attachments"]
            except Exception as exc:
                logger.error("Failed to fetch attachments for page %s: %s", page_id, exc, exc_info=True)
                results[page_id] = []          # or {"error": str(exc)} if you want the dict shape

        return results

    # -------------------------------

    def get_content_for_pages(self, page_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Retrieves the full page data object (including body.storage) for a list of
        page IDs in parallel. Returns a dictionary mapping each page ID to its data.
        """
        if not page_ids:
            return {}

        results: Dict[str, Dict[str, Any]] = {}
        future_to_page_id = {
            self._executor.submit(self.get_page_content, page_id): page_id
            for page_id in page_ids
        }

        for future in as_completed(future_to_page_id):
            page_id = future_to_page_id[future]
            try:
                page_data = future.result()
                if page_data:
                    results[page_id] = page_data
            except Exception as exc:
                logger.error(f"A task for fetching page content for {page_id} generated an exception: {exc}", exc_info=True)
                raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{page_id}", {})

        return results

    def get_page_referenced_attachments(self, page_id: str) -> list[dict[str, Any]]:
        """Retrieves only the attachments that are referenced in the page content."""
        logger.debug("Fetching page content and attachments for page %s.", page_id)
        page_data = self.get_page_content(page_id, expand="body.storage,children.attachment")
        content_html = page_data.get('body', {}).get('storage', {}).get('value', '')
        attachment_results = page_data.get('children', {}).get('attachment', {}).get('results', [])
        current_attachments = self._filter_current_attachments(attachment_results)

        if not current_attachments or not content_html:
            return []

        # Avoid parsing excessively large pages to prevent performance issues
        max_size = self.config.file_processing.max_content_size_for_filtering
        content_bytes_len = len(content_html.encode('utf-8'))
        if content_bytes_len > max_size:
            logger.warning("Page %s content is too large (%d bytes); skipping attachment filtering.", page_id, content_bytes_len)
            return []

        return self._extract_attachments_from_xml(content_html, current_attachments)

    @lru_cache(maxsize=512)
    def _fetch_attachment_meta_from_api(self, attachment_id: str) -> dict[str, Any]:
        """Fetches attachment metadata from the API, with authentication and caching."""
        api_url = f"{self.url}/rest/api/content/{attachment_id}"
        with self._safe_request_context('GET', api_url) as resp:
            return resp.json()

    # -------------------------------
    # XML helpers
    # -------------------------------
    @staticmethod
    def _extract_attachments_from_xml(content: str, attachments: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        Parses Confluence storage format (XML) to find referenced attachment filenames.
        Note: For production robustness against malformed HTML, consider replacing
        `xml.etree.ElementTree` with a more lenient parser like `lxml.html` or `html5lib`.
        """
        NS = {
            'ri': 'http://www.atlassian.com/schema/confluence/4/ri/',
            'ac': 'http://www.atlassian.com/schema/confluence/4/ac/'
        }
        try:
            stripped = content.strip()
            if not stripped.startswith(('<?xml', '<html')):
                stripped = f'<root xmlns:ac="{NS["ac"]}" xmlns:ri="{NS["ri"]}">{stripped}</root>'

            root = ET.fromstring(stripped)
            referenced_filenames = set()

            # Find filenames in draw.io macros
            for macro in root.findall(".//ac:structured-macro[@ac:name='drawio']", NS):
                for param in macro.findall(".//ac:parameter[@ac:name='diagramName']", NS):
                    if param.text:
                        referenced_filenames.add(param.text.strip())

            # Find filenames in standard <ri:attachment> tags
            for ri_att in root.findall(".//ri:attachment", NS):
                filename = ri_att.get(f"{{{NS['ri']}}}filename")
                if filename:
                    referenced_filenames.add(filename.strip())

            lower_refs = {f.lower() for f in referenced_filenames}
            return [att for att in attachments if att.get('title', '').lower() in lower_refs]
        except ET.ParseError as e:
            logger.warning("XML parsing failed for attachment filtering: %s", e)
            return []

    # -------------------------------
    # Public methods
    # -------------------------------
    def get_attachment_by_id(self, attachment_id: str) -> AttachmentMeta:
        """Gets attachment metadata, including a direct download link."""
        try:
            raw_meta = self._fetch_attachment_meta_from_api(attachment_id)
            link = raw_meta.get('_links', {}).get('download')
            if not link:
                raise ConfluenceNotFoundError(f"No download link for attachment {attachment_id}", resource=attachment_id)
            full_link = self.url + link if link.startswith('/') else link
            return AttachmentMeta(id=attachment_id, download_link=full_link)
        except Exception as exc:
            logger.error(f"Error fetching attachment meta for id {attachment_id}: {exc}", exc_info=True)
            raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{attachment_id}/attachment", {})

    def _stream_content_from_url(self, url: str, chunk_size: int) -> Iterator[bytes]:
        """Helper to stream content from a URL with the client's session."""
        with self._safe_request_context('GET', url, stream=True) as resp:
            for chunk in resp.iter_content(chunk_size=chunk_size):
                yield chunk

    def stream_attachment_content(self, attachment_id: str, chunk_size: int = 8192) -> Iterator[bytes]:
        """Streams the binary content of an attachment in chunks."""
        try:
            meta = self.get_attachment_by_id(attachment_id)
            return self._call_with_retry(
                self._stream_content_from_url, url=meta.download_link, chunk_size=chunk_size
            )
        except Exception as exc:
            logger.error(f"Error streaming attachment content for id {attachment_id}: {exc}", exc_info=True)
            raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{attachment_id}/download", {})

    def get_page_content(self, page_id: str,
                         expand: str = "body.storage,version,ancestors,children.attachment") -> dict[str, Any]:
        """Retrieves the content and metadata for a single page."""
        try:
            return self._call_with_retry(self.confluence.get_page_by_id, page_id=page_id, expand=expand) or {}
        except Exception as exc:
            logger.error(f"Error fetching page content for page {page_id}: {exc}", exc_info=True)
            raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{page_id}", {})

    def get_page_content(self, page_id: str,
                         expand: str = "body.storage,version,ancestors,children.attachment") -> dict[str, Any]:
        """Retrieves the content and metadata for a single page."""
        try:
            return self._call_with_retry(self.confluence.get_page_by_id, page_id=page_id, expand=expand) or {}
        except Exception as exc:
            logger.error(f"Error fetching page content for page {page_id}: {exc}", exc_info=True)
            raise self._wrap_exception(exc, f"{self.url}/rest/api/content/{page_id}", {})

    def search_content(self, cql: str, limit: int = 100, start: int = 0,
                       expand: str = "version,ancestors,metadata.labels,body.storage") -> list[dict[str, Any]]:
        """
        Performs a CQL search using a direct `requests` call to the /content/search
        endpoint, which is required for body expansion on some Confluence Server versions.
        """
        logger.debug("Performing CQL search using direct requests against /content/search.")

        api_url = f"{self.url}/rest/api/content/search"
        params = {
            'cql': cql,
            'limit': limit,
            'start': start,
            'expand': expand
        }
        retryer = create_search_retryer(self.config)

        def _make_request():
            with self._safe_request_context('GET', api_url, params=params) as resp:
                return resp.json()

        try:
            response_data = retryer(_make_request)
            return response_data.get("results", [])
        except Exception as exc:
            logger.error(f"Direct CQL search failed: {exc}", exc_info=True)
            raise self._wrap_exception(exc, api_url, {})

    def stream_page_as_pdf(self, page_id: str, chunk_size: int = 8192) -> Iterator[bytes]:
        """Streams a Confluence page export as a PDF file."""
        try:
            logger.info("Initiating PDF export stream for page %s", page_id)
            # We use a direct request to ensure streaming is possible
            pdf_export_url = f"{self.url}/rest/api/content/{page_id}/export/pdf"
            return self._call_with_retry(
                self._stream_content_from_url, url=pdf_export_url, chunk_size=chunk_size
            )
        except Exception as exc:
            logger.error(f"Error streaming page {page_id} as PDF: {exc}", exc_info=True)
            raise self._wrap_exception(exc, pdf_export_url, {})
