"""Confluence configuration helper functions.

This module contains business logic and helper functions that work with
Confluence configuration objects.
"""
import logging
import os
from datetime import datetime, timedelta, UTC
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .schema import ConfluenceConfig

logger = logging.getLogger(__name__)

def should_include_attachment(config: "ConfluenceConfig", attachment_name: str) -> bool:
    """
    Checks if an attachment should be included based on configured file extensions.

    Args:
        config: The ConfluenceConfig instance.
        attachment_name: Name of the attachment to check.

    Returns:
        True if the attachment should be included, False otherwise.
    """
    if not config.attachments.include_attachments or not config.attachments.file_extensions:
        return False

    allowed_extensions = config.attachments.file_extensions
    _, ext = os.path.splitext(attachment_name.lower())
    file_ext = ext.lstrip('.')

    normalized_allowed = {e.lower().lstrip('.') for e in allowed_extensions}

    return file_ext in normalized_allowed


def should_include_by_labels(config: "ConfluenceConfig", page_labels: list[str]) -> bool:
    """
    Checks if a page should be included based on its labels.

    Args:
        config: The ConfluenceConfig instance.
        page_labels: A list of labels for the page.

    Returns:
        True if the page should be included, False otherwise.
    """
    # Exclude takes precedence
    if config.filtering.exclude_labels and any(label in config.filtering.exclude_labels for label in page_labels):
        return False

    # If include labels are defined, at least one must match
    if config.filtering.labels:
        return any(label in config.filtering.labels for label in page_labels)

    # If no label filters are set, or if exclude check passed and no include labels are set
    return True

def is_temp_file(config: "ConfluenceConfig", filename: str) -> bool:
    """Checks if a file is temporary based on configured extensions."""
    return filename.lower().endswith(tuple(config.file_processing.temp_extensions))


def contains_html_indicators(config: "ConfluenceConfig", content: bytes) -> bool:
    """Checks if byte content contains configured HTML indicators."""
    content_lower = content.lower()
    return any(indicator in content_lower for indicator in config.file_processing.html_indicators)


def is_filename_too_long(config: "ConfluenceConfig", filename: str) -> bool:
    """Checks if a filename exceeds the configured maximum length."""
    return len(filename) > config.file_processing.max_filename_length


def should_include_by_date(config: "ConfluenceConfig", modification_date: datetime) -> bool:
    """
    Checks if content should be included based on its last modification date.

    Args:
        config: The ConfluenceConfig instance.
        modification_date: A timezone-aware datetime object for the modification date.

    Returns:
        True if the content should be included, False otherwise.
    """
    if not config.filtering.last_modified_days:
        return True

    # Ensure modification_date is timezone-aware for a correct comparison
    if modification_date.tzinfo is None:
        logger.warning(
            "Naive datetime %s treated as UTC. "
            "Provide timezone-aware datetimes to avoid surprises.",
            modification_date
        )
        modification_date = modification_date.replace(tzinfo=UTC)

    cutoff_date = datetime.now(UTC) - timedelta(days=config.filtering.last_modified_days)
    return modification_date >= cutoff_date


def create_minimal_config() -> "ConfluenceConfig":
    """
    Creates a minimal, valid config object for testing scenarios.

    Returns:
        A ConfluenceConfig instance with safe defaults for testing.
    """
    from .schema import BasicConfig, ConfluenceConfig

    # Instantiate directly for clarity and to respect Pydantic's model construction
    config = ConfluenceConfig(
        basic=BasicConfig(spaces=["DEFAULT_SPACE"]),
    )
    return config
