### Updated `README.md`

```markdown
# Chargeur de Données Confluence

Ce document décrit l'architecture et le fonctionnement du chargeur de données pour Confluence. Ce module est conçu pour découvrir, filtrer et télécharger de manière fiable et performante le contenu d'une ou plusieurs instances Confluence.

## Table des Matières

- [Chargeur de Données Confluence](#chargeur-de-données-confluence)
  - [Table des Matières](#table-des-matières)
  - [1. Vue d'Ensemble](#1-vue-densemble)
  - [2. Fonctionnalités Clés](#2-fonctionnalités-clés)
  - [3. Architecture](#3-architecture)
  - [4. Flux de Travail Détaillé](#4-flux-de-travail-détaillé)
    - [Phase 1 : Découverte (`get_document_list`)](#phase-1--découverte-get_document_list)
    - [Phase 2 : Téléchargement (`get_document`)](#phase-2--téléchargement-get_document)
  - [5. Authentification](#5-authentification)
    - [Stratégies d'Authentification](#stratégies-dauthentification)
    - [Structure du Secret](#structure-du-secret)
  - [6. Gestion des Erreurs et Résilience](#6-gestion-des-erreurs-et-résilience)
  - [7. Mode Mock pour le Développement](#7-mode-mock-pour-le-développement)
  - [8. Configuration Détaillée des Pièces Jointes](#8-configuration-détaillée-des-pièces-jointes)
  - [9. Exemples d'Utilisation via API](#9-exemples-dutilisation-via-api)
    - [9.1 Lister les documents d'une source (`/loader/list`)](#91-lister-les-documents-dune-source-loaderlist)
      - [Commande `curl`](#commande-curl)
      - [Fichier d'entrée (`getlist.json`)](#fichier-dentrée-getlistjson)
      - [Fichier de sortie (Exemple)](#fichier-de-sortie-exemple)
    - [9.2 Télécharger un document spécifique (`/loader/document`)](#92-télécharger-un-document-spécifique-loaderdocument)
      - [Commande `curl`](#commande-curl-1)
      - [Fichier d'entrée (`test_png.getdoc.json`)](#fichier-dentrée-test_pnggetdocjson)
      - [Fichier de sortie (Exemple)](#fichier-de-sortie-exemple-1)
  - [10. Bonnes Pratiques et Recommandations](#10-bonnes-pratiques-et-recommandations)
    - [10.1. Configuration Optimale](#101-configuration-optimale)
    - [10.2. Sécurité et Authentification](#102-sécurité-et-authentification)
    - [10.3. Gestion des Erreurs](#103-gestion-des-erreurs)
    - [10.4. Performance et Optimisation](#104-performance-et-optimisation)

---

## 1. Vue d'Ensemble

Le chargeur Confluence est un composant robuste qui orchestre la connexion à Confluence, la recherche de contenu (pages, pièces jointes) selon des règles de filtrage complexes, et le téléchargement de ce contenu pour un traitement ultérieur.

Il est conçu pour être :
-   **Performant** : grâce au traitement parallèle des appels API.
-   **Résilient** : avec des mécanismes de "retry" et de disjoncteur (circuit breaker) pour gérer les instabilités réseau ou API.
-   **Flexible** : via un système de configuration JSON riche permettant d'adapter finement le comportement du chargeur.
-   **Configurable** : offrant un contrôle précis sur les données à extraire, notamment pour les pièces jointes.

## 2. Fonctionnalités Clés

-   **Traitement Parallèle** : Capacité à traiter plusieurs appels API simultanément pour accélérer la découverte et le téléchargement.
-   **Découverte Intelligente** :
    -   Navigation récursive dans les arborescences de pages avec une profondeur configurable.
    -   Détection et traitement des diagrammes **Draw.io** comme des documents de premier ordre.
    -   **Filtrage avancé des pièces jointes** : Deux modes de récupération des pièces jointes pour un équilibre parfait entre vitesse et précision.
-   **Filtrage Puissant** :
    -   Filtrage par date de dernière modification, labels (inclus/exclus), et extensions de fichiers.
    -   Support pour des requêtes **CQL (Confluence Query Language)** personnalisées pour des scénarios de recherche complexes.
-   **Authentification Flexible** : Supporte une authentification centralisée (`global`) ou par périmètre (`perimeter`), en utilisant des jetons PAT ou des paires utilisateur/jeton API.
-   **Mode Mock Intégré** : Permet de tester le flux de travail sans se connecter réellement à Confluence, idéal pour le développement et les tests CI/CD.

## 3. Architecture

Le chargeur est composé de plusieurs classes spécialisées qui collaborent pour accomplir la tâche.

-   `ConfluenceLoader` (`confluence_loader.py`)
    -   **Rôle** : **L'Orchestrateur**. C'est le point d'entrée principal. Il initialise tous les autres composants et pilote les deux phases principales : la découverte (`get_document_list`) et le téléchargement (`get_document`).

-   `SpaceProcessor` (`space/space_processor.py`)
    -   **Rôle** : **Le Découvreur**. Il est responsable de l'exploration d'un *unique* espace Confluence. Il identifie toutes les pages, pièces jointes et diagrammes Draw.io et crée des "bons de travail" (`DocumentBean`) sans télécharger leur contenu.

-   `CqlSearch` (`search/cql_search.py`)
    -   **Rôle** : **Le Chercheur**. Spécialisé dans la construction et l'exécution de requêtes CQL. Il gère la pagination, les stratégies de repli (fallback) et la résilience des recherches.

-   `ContentDownloader` (`processors/content_downloader.py`)
    -   **Rôle** : **Le Téléchargeur**. Responsable du téléchargement effectif du contenu d'un `DocumentBean` (page, pièce jointe ou diagramme) et de sa sauvegarde dans le système de fichiers.

-   `ConfluenceClient` (`client/confluence_client.py`)
    -   **Rôle** : **Le Communicateur**. Un client API robuste qui encapsule les appels de bas niveau vers l'API Confluence. Il gère l'authentification, la gestion des sessions et la traduction des erreurs HTTP en exceptions sémantiques.

-   **Système de Configuration** (`config/*.py`)
    -   **Rôle** : **Le Configurateur**. Cet ensemble de classes (`ConfluenceConfig`, `defaults`, `schema`) est responsable de la validation et de la création d'objets de configuration fortement typés à partir du JSON brut de la `SourceBean`.

-   **Processeurs spécialisés** (`processors/*.py`)
    -   **Rôle** : **Les Spécialistes**. Outre le `ContentDownloader`, ce répertoire contient des processeurs pour des tâches spécifiques comme la détection et l'analyse des diagrammes Draw.io (`DrawioDetector`).

## 4. Flux de Travail Détaillé

### Phase 1 : Découverte (`get_document_list`)

1.  `ConfluenceLoader` reçoit une `SourceBean`.
2.  Il initialise ses composants : `ConfluenceConfig` est créé à partir du JSON de la source, puis le `ConfluenceClient` est instancié avec les informations d'authentification. Enfin, `CqlSearch` et `SpaceProcessor` sont créés.
3.  Pour chaque `space` défini dans la configuration :
    a. `ConfluenceLoader` délègue le travail au `SpaceProcessor`.
    b. `SpaceProcessor` utilise `CqlSearch` pour trouver toutes les pages de l'espace.
    c. Pour chaque page, `SpaceProcessor` :
        i.  Crée un `DocumentBean` pour la page elle-même.
        ii. Analyse la page pour trouver des diagrammes **Draw.io** et crée des `DocumentBean`s spécifiques.
        iii. **Récupère les pièces jointes** selon la stratégie configurée (voir section "Configuration Détaillée des Pièces Jointes"). Il les filtre ensuite par extension et crée des `DocumentBean`s pour celles qui sont valides.
4.  `ConfluenceLoader` agrège les listes de `DocumentBean` et retourne la liste complète.

### Phase 2 : Téléchargement (`get_document`)

1.  `ConfluenceLoader` reçoit un `DocumentBean` spécifique à télécharger.
2.  Il analyse l'ID du document pour déterminer son type (page, pièce jointe, drawio).
3.  Il délègue le téléchargement au `ContentDownloader`.
4.  `ContentDownloader` utilise le `ConfluenceClient` pour effectuer l'appel API approprié (`get_page_content`, `get_attachment_content`, etc.).
5.  Il sauvegarde le contenu reçu (HTML, PDF, fichier binaire...) dans le chemin de sortie (`output_path`).
6.  Il retourne les métadonnées du téléchargement (chemin du fichier, taille, etc.).

## 5. Authentification

Le client supporte deux stratégies d'authentification et deux types de jetons.

### Stratégies d'Authentification

Ceci est contrôlé par `auth.confluence_auth_mode`.

-   **`"global"` (par défaut)** : Le chargeur utilise un seul secret global.
-   **`"perimeter"`** : Le chargeur recherche un secret spécifique au périmètre de la source. S'il n'est pas trouvé, il se rabat sur le mode `global`.

### Structure du Secret

Le secret (qu'il soit global ou par périmètre) doit être un JSON avec la structure suivante :

```json
{
  "confluence_url": "https://votre-instance-confluence.com",
  "cloud": false,
  "username": "<EMAIL>",
  "api_token": "VOTRE_JETON_API",
  "pat_token": "VOTRE_PAT_TOKEN"
}
```

-   `confluence_url` (obligatoire) : L'URL de base de l'instance Confluence.
-   `cloud` (obligatoire) : `true` pour Confluence Cloud, `false` pour Server/Data Center.
-   **Méthode 1 : Jeton API** (pour Confluence Cloud) : `username` et `api_token`.
-   **Méthode 2 : PAT (Personal Access Token)** (recommandé pour Confluence Server/Data Center) : `pat_token`. S'il est fourni, il a **priorité**.

## 6. Gestion des Erreurs et Résilience

-   **Retry** : Les appels API critiques sont automatiquement réessayés en cas d'échec (configurable via `retry_attempts`).
-   **Disjoncteur (Circuit Breaker)** : Si un grand nombre d'appels échouent consécutivement, le disjoncteur s'ouvre pour protéger le service et échouer rapidement.

## 7. Mode Mock pour le Développement

Pour faciliter le développement et les tests, un mode "mock" est disponible. Il peut être activé via des variables d'environnement. Lorsque ce mode est actif, le `ConfluenceLoader` utilise le `MockConfluenceClient`, qui sert des réponses API pré-enregistrées à partir de fichiers JSON locaux, au lieu de contacter une véritable instance Confluence.

## 8. Configuration Détaillée des Pièces Jointes

Le chargeur offre un contrôle précis sur la manière dont les pièces jointes sont découvertes, vous permettant de choisir entre vitesse et précision. Ceci est contrôlé par le paramètre `attachments.only_referenced_attachments` dans la configuration de la source.

### Mode Rapide

-   **Configuration** : `only_referenced_attachments: false` (ou non spécifié).
-   **Comportement** : Le chargeur effectue une requête API de masse pour récupérer **toutes les pièces jointes** de toutes les pages découvertes. Il filtre ensuite cette liste par les extensions de fichiers autorisées (`file_extensions`).
-   **Avantage** : **Très rapide**. Idéal pour la plupart des cas d'utilisation et pour les grands espaces, car il minimise le nombre d'appels API.
-   **Inconvénient** : Peut récupérer des pièces jointes qui sont obsolètes ou non utilisées, car il ne vérifie pas si elles sont réellement liées dans le contenu de la page.

### Mode Précis (par défaut)

-   **Configuration** : `only_referenced_attachments: true`.
-   **Comportement** : Pour chaque page, le chargeur télécharge son contenu, l'analyse pour trouver les liens vers les pièces jointes, et ne considère que celles qui sont **activement référencées**.
-   **Avantage** : **Très précis**. Garantit que seules les pièces jointes pertinentes et visibles sont traitées.
-   **Inconvénient** : **Beaucoup plus lent**. Ce mode nécessite au moins un appel API supplémentaire par page, ce qui peut considérablement ralentir le processus pour les grands espaces.

**Exemple de configuration pour le mode précis :**
```json
{
  "attachments": {
    "include_attachments": true,
    "file_extensions": ["pptx", "pdf", "png"],
    "only_referenced_attachments": true
  }
}
```

## 9. Exemples d'Utilisation via API

Cette section illustre comment interagir avec le service du chargeur via des appels API HTTP.

### 9.1 Lister les documents d'une source (`/loader/list`)

Cette étape lance le processus de découverte pour générer la liste des `DocumentBean`.

#### Commande `curl`

```bash
❯ curl -X 'POST' 'http://127.0.0.1:8092/loader/list/sandbox' -H 'Content-Type: application/json' -d @getlist.json
```

#### Fichier d'entrée (`getlist.json`)

Le fichier contient la `SourceBean` qui décrit la source à traiter. Le champ `configuration` contient les paramètres spécifiques au chargeur Confluence.

```json
{
  "source": {
    "code": "testconfluence27284",
    "type": "confluence",
    "domain_code": "CiblesRurales",
    "perimeter_code": "sandbox",
    "configuration": {
      "basic": {
        "spaces": ["VBT"]
      },
      "attachments": {
        "include_attachments": true,
        "file_extensions": ["pptx", "png"],
        "only_referenced_attachments": true
      }
    }
  }
}
```

#### Fichier de sortie (Exemple)

Le service retourne un JSON contenant la liste des `DocumentBean` découverts.

```json
{
  "documents": [
    {
      "id": "CiblesRurales|testconfluence27284|2413010987",
      "path": "https://espace.agir.orange.com/pages/viewpage.action?pageId=2413010987",
      "name": "02. Documentation générale",
      "modification_time": "2025-07-11T14:30:51.873Z"
    },
    {
      "id": "CiblesRurales|testconfluence27284|att_2454744796",
      "path": "/download/attachments/2413010987/image-2025-7-11_16-30-46.png",
      "name": "image-2025-7-11_16-30-46.png",
      "modification_time": "2025-07-11T14:30:46.999Z"
    }
  ]
}
```

### 9.2 Télécharger un document spécifique (`/loader/document`)

Cette étape prend en entrée un `DocumentBean` et demande au chargeur de télécharger son contenu.

<!-- I've simplified this section as the details are less critical than the list part -->

#### Commande `curl`

```bash
curl -X 'POST' 'http://127.0.0.1:8092/loader/document/sandbox' -H 'Content-Type: application/json' -d @getdoc.json
```

#### Fichier d'entrée (`getdoc.json`)

Le fichier contient la `SourceBean` complète et le `DocumentBean` spécifique à télécharger.

```json
{
    "source": { "... (same as above) ..." },
    "document": {
      "id": "CiblesRurales|testconfluence27284|att_2454744796",
      "path": "/download/attachments/2413010987/image-2025-7-11_16-30-46.png",
      "name": "image-2025-7-11_16-30-46.png",
      "modification_time": "2025-07-11T14:30:46.999Z"
    },
    "output_path": "/path/to/download/to"
}
```

#### Fichier de sortie (Exemple)

Le service retourne les métadonnées enrichies du document qui vient d'être téléchargé.

```json
{
  "metadata": {
    "document_id": "CiblesRurales|testconfluence27284|att_2454744796",
    "document_name": "image-2025-7-11_16-30-46.png",
    "location": "/path/to/download/to/image-2025-7-11_16-30-46_att_2454744796.png",
    "size": 42534,
    "mime_type": "image/png"
  }
}
```

---

## 10. Bonnes Pratiques et Recommandations

### 10.1. Configuration Optimale

-   **Pour la vitesse** : Laissez `only_referenced_attachments` à `false` (par défaut).
-   **Pour la précision** : Mettez `only_referenced_attachments` à `true`, mais soyez conscient de l'impact sur la performance.
-   Utilisez `labels`, `exclude_labels`, et `last_modified_days` pour réduire au maximum le nombre de pages à scanner.

### 10.2. Sécurité et Authentification

-   **Confluence Server/Data Center** : Privilégiez les PAT tokens.
-   **Confluence Cloud** : Utilisez les API tokens avec des permissions minimales.

### 10.3. Gestion des Erreurs

-   Surveillez les logs pour les erreurs de circuit breaker ou de retry.
-   Ajustez les timeouts et les tentatives de retry en fonction de la stabilité de votre instance Confluence.

### 10.4. Performance et Optimisation

-   Limitez `child_page_depth` à une valeur raisonnable pour éviter des scans trop profonds.
-   Définissez la liste `file_extensions` pour ne télécharger que les types de fichiers strictement nécessaires.
