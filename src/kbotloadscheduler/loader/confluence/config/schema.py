"""Pydantic-based schema for Confluence configuration.

This module defines the configuration structure, types, defaults, and
validation rules in a declarative way using Pydantic V2.
"""
from typing import Literal, Any
from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic import PositiveInt, NonNegativeInt

from . import defaults

# ---- Helpers ---------------------------------------------------------------
def _split_str_on_comma(value: Any) -> Any | list[str]:
    """Pydantic validator helper to convert a comma-separated string to a list."""
    if isinstance(value, str):
        return [item.strip() for item in value.split(",") if item.strip()]
    return value

# ---- literal/constant aliases (computed once at import) --------------------
AuthMode                  = Literal[*defaults.VALID_AUTH_MODES]
ExportFormat              = Literal[*defaults.VALID_EXPORT_FORMATS]
DuplicateStrategy         = Literal[*defaults.VALID_DUPLICATE_FILENAME_STRATEGIES]

_MIN_FILENAME_LENGTH      = defaults.MIN_FILENAME_LENGTH
_DEFAULT_MAX_FILENAME_LENGTH = defaults.DEFAULT_MAX_FILENAME_LENGTH

# --- Models ------------------------------------------------------------------
class AuthConfig(BaseModel):
    """Authentication configuration for Confluence."""
    confluence_auth_mode: AuthMode = defaults.DEFAULT_AUTH_MODE

class AttachmentConfig(BaseModel):
    """Attachment handling configuration."""
    include_attachments: bool = defaults.DEFAULT_INCLUDE_ATTACHMENTS
    file_extensions: list[str] = Field(default_factory=lambda: list(defaults.DEFAULT_ALLOWED_FILE_EXTENSIONS))
    extract_drawio_as_documents: bool = defaults.DEFAULT_EXTRACT_DRAWIO
    include_drawio_png_exports: bool = defaults.DEFAULT_INCLUDE_DRAWIO_PNG

    only_referenced_attachments: bool = Field(
        default=defaults.DEFAULT_ONLY_REFERENCED_ATTACHMENTS,
        description="If True, only download attachments that are actively referenced in the page's content. "
                    "WARNING: This is much slower as it requires fetching and parsing every page body."
    )

    _split_extensions = field_validator("file_extensions", mode="before")(_split_str_on_comma)

class FilteringConfig(BaseModel):
    """Content filtering configuration."""
    labels: list[str] | None = None
    exclude_labels: list[str] | None = None
    last_modified_days: PositiveInt | None = None
    custom_cql: str | None = None

    _split_labels = field_validator("labels", "exclude_labels", mode="before")(_split_str_on_comma)

class BasicConfig(BaseModel):
    """Basic Confluence configuration."""
    spaces: list[str] = Field(default_factory=list)
    max_results: PositiveInt | None = None
    export_format: ExportFormat = defaults.DEFAULT_EXPORT_FORMAT
    include_content_in_metadata: bool = defaults.DEFAULT_INCLUDE_CONTENT_IN_METADATA
    include_child_pages: bool = defaults.DEFAULT_INCLUDE_CHILD_PAGES
    child_page_depth: NonNegativeInt = defaults.DEFAULT_CHILD_PAGE_DEPTH

class PerformanceConfig(BaseModel):
    """Performance and reliability configuration."""
    enable_caching: bool = defaults.DEFAULT_ENABLE_CACHING
    cache_ttl_minutes: PositiveInt = defaults.DEFAULT_CACHE_TTL_MINUTES
    retry_attempts: NonNegativeInt = defaults.DEFAULT_RETRY_ATTEMPTS
    retry_delay_seconds: NonNegativeInt = defaults.DEFAULT_RETRY_DELAY_SECONDS
    circuit_breaker_threshold: PositiveInt = defaults.DEFAULT_CIRCUIT_BREAKER_THRESHOLD
    circuit_breaker_timeout_seconds: PositiveInt = defaults.DEFAULT_CIRCUIT_BREAKER_TIMEOUT_SECONDS
    enable_metrics: bool = defaults.DEFAULT_ENABLE_METRICS

    max_parallel_workers: PositiveInt = Field(
        default=defaults.DEFAULT_MAX_PARALLEL_WORKERS,
        description="Maximum number of parallel threads for concurrent API calls like fetching attachments."
    )
    fail_fast: bool = Field(
        default=defaults.DEFAULT_FAIL_FAST,
        description="If True, the process will stop immediately on the first major API error instead of just logging a warning."
    )

class FileProcessingConfig(BaseModel):
    """File processing configuration."""
    duplicate_filename_strategy: DuplicateStrategy = defaults.DEFAULT_DUPLICATE_FILENAME_STRATEGY
    temp_extensions: list[str] = Field(default_factory=lambda: list(defaults.DEFAULT_TEMP_EXTENSIONS))
    html_indicators: list[bytes] = Field(default_factory=lambda: list(defaults.DEFAULT_HTML_INDICATORS))
    max_filename_length: int = Field(_DEFAULT_MAX_FILENAME_LENGTH, ge=_MIN_FILENAME_LENGTH)
    default_timeout: PositiveInt = defaults.DEFAULT_TIMEOUT
    max_content_size_for_filtering: PositiveInt = defaults.DEFAULT_MAX_CONTENT_SIZE

class ConfluenceConfig(BaseModel):
    """Main configuration class for the Confluence loader."""
    auth: AuthConfig = Field(default_factory=AuthConfig)
    basic: BasicConfig = Field(default_factory=BasicConfig)
    attachments: AttachmentConfig = Field(default_factory=AttachmentConfig)
    filtering: FilteringConfig = Field(default_factory=FilteringConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    file_processing: FileProcessingConfig = Field(default_factory=FileProcessingConfig)

    @model_validator(mode='after')
    def check_spaces_is_not_empty(self) -> 'ConfluenceConfig':
        if not self.basic.spaces:
            raise ValueError(
                "At least one Confluence space must be specified via the 'spaces' "
                "or legacy 'space_key' configuration parameter."
            )
        return self
