"""Configuration Builder for Confluence.

This module provides a single entry point for creating a validated
ConfluenceConfig object from a raw source bean. It encapsulates
transformation, backward-compatibility, and instantiation logic.
"""

import logging
from typing import Any, Dict

from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.loader.confluence.exceptions import ConfluenceConfigurationError
from pydantic import ValidationError

from .schema import (
    AuthConfig,
    AttachmentConfig,
    BasicConfig,
    ConfluenceConfig,
    FileProcessingConfig,
    FilteringConfig,
    PerformanceConfig,
)

logger = logging.getLogger(__name__)


_MODEL_MAP = {
    "auth": AuthConfig,
    "basic": BasicConfig,
    "attachments": AttachmentConfig,
    "filtering": FilteringConfig,
    "performance": PerformanceConfig,
    "file_processing": FileProcessingConfig,
}


def _nest_flat_keys(flat_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Dynamically transforms a flat config dictionary to a nested structure.

    It inspects the Pydantic models defined in _MODEL_MAP to determine
    which section each key belongs to. Keys not found in any model are
    kept at the top level.
    """
    nested_config: Dict[str, Any] = {}
    known_fields: Dict[str, str] = {
        field_name: section
        for section, model in _MODEL_MAP.items()
        for field_name in model.model_fields
    }

    for key, value in flat_config.items():
        section = known_fields.get(key)
        if section:
            nested_config.setdefault(section, {})[key] = value
        else:
            # Place unknown keys at the top level for backward compatibility
            # or for Pydantic to handle at the root level of ConfluenceConfig.
            nested_config[key] = value

    return nested_config


def build_config_from_source(source: SourceBean) -> ConfluenceConfig:
    """
    Creates a validated instance of ConfluenceConfig from a SourceBean.

    This function orchestrates the entire build process:
    1. Parses the raw configuration from the source.
    2. Nests the flat key-value pairs into a structured dictionary.
    3. Applies backward-compatibility transformations (e.g., `space_key`).
    4. Validates the final structure and instantiates the ConfluenceConfig object.
       Pydantic handles type coercion and custom parsing during this step.
    """
    flat_config = source.parse_configuration()

    # 1. Transform flat structure to a nested one that matches the schema.
    nested_config = _nest_flat_keys(flat_config)

    # 2. Apply backward-compatibility for `space_key` -> `basic.spaces`.
    # Pydantic's validator will later ensure that `spaces` is not empty.
    basic_section = nested_config.setdefault("basic", {})
    if "space_key" in flat_config:
        space_key = flat_config.get("space_key")
        if space_key:
            # Add to the list, avoiding duplicates if already present.
            spaces = basic_section.setdefault("spaces", [])
            if space_key not in spaces:
                spaces.append(space_key)

    # 3. Validate the final nested structure and instantiate the config object.
    # Pydantic will automatically handle type conversion (str -> int),
    # custom parsing (comma-separated str -> list), and validation rules.
    try:
        config = ConfluenceConfig.model_validate(nested_config)
        logger.debug(f"Validated configuration created for source '{source.code}'.")
        return config
    except ValidationError as e:
        # Wrap Pydantic's detailed error in a domain-specific exception.
        raise ConfluenceConfigurationError(f"Configuration validation failed: {e}") from e
