"""Configuration management for Confluence loader.

This module provides a Pydantic-based configuration schema and a builder
for creating validated Confluence configuration objects.

Example usage:
from kbotloadscheduler.confluence.config import defaults
defaults.DEFAULT_ATTACHMENT_LIMIT
"""

from .schema import (
    ConfluenceConfig,
    AuthConfig,
    AttachmentConfig,
    FilteringConfig,
    BasicConfig,
    PerformanceConfig,
    FileProcessingConfig,
)
from . import defaults
from .helpers import (
    should_include_attachment,
    should_include_by_labels,
    is_temp_file,
    contains_html_indicators,
    is_filename_too_long,
    should_include_by_date,
    create_minimal_config,
)
from .builder import build_config_from_source

__all__ = [
    # Main entry point for creating a config object
    "build_config_from_source",
    # Main configuration schema
    "ConfluenceConfig",
    # Nested configuration models
    "AuthConfig",
    "AttachmentConfig",
    "FilteringConfig",
    "BasicConfig",
    "PerformanceConfig",
    "FileProcessingConfig",
    # Helper functions
    "should_include_attachment",
    "should_include_by_labels",
    "is_temp_file",
    "contains_html_indicators",
    "is_filename_too_long",
    "should_include_by_date",
    "create_minimal_config",
]
