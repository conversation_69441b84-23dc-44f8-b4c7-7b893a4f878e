"""
Main loader for Confluence.

This class is the entry point for loading documents from Confluence.
It orchestrates operations by delegating to specialized classes for
configuration, search, and processing of documents and spaces.
"""

import hashlib
import json
import logging
import threading
from typing import Any, Dict, List, Optional
from pybreaker import CircuitBreaker

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean, Metadata
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

from ..abstract_loader import AbstractLoader
from kbotloadscheduler.loader.confluence.exceptions import (
    ConfluenceAuthenticationError,
    ConfluenceClientException,
    ConfluenceConfigurationError,
)
from .config.builder import build_config_from_source as build_confluence_config
from .config.schema import ConfluenceConfig
from .processors.content_downloader import ContentDownloader
from .search.cql_search import CqlSearch
from .space.space_processor import SpaceProcessor
from .utils.document_id_utils import DocumentIdFormatter
# We keep this import, as the loader needs to know the type of the client.
from .client.confluence_client import ConfluenceClient


class ConfluenceLoader(AbstractLoader):
    """
    Loads documents from a Confluence instance by processing configured
    spaces, their pages, and their attachments. This class is thread-safe.
    """

    def __init__(self, config: ConfigWithSecret, client: Optional[ConfluenceClient] = None):
        """
        Initialize the Confluence loader.
        Accepts an optional client for dependency injection, which is ideal for testing/mocking.
        """
        super().__init__("confluence")
        self.global_config = config
        self._lock = threading.Lock() # Initialize the lock for thread-safety

        # If a client is provided (injected), use it. Otherwise, it will be created on first use.
        self.client: Optional[ConfluenceClient] = client

        # These components will be initialized in _setup_for_source
        self.downloader: Optional[ContentDownloader] = None
        self.space_processor: Optional[SpaceProcessor] = None
        self.config: Optional[ConfluenceConfig] = None

        # State trackers to manage re-initialization.
        self._current_source_code: Optional[str] = None
        self._config_fingerprint: Optional[str] = None

    def _setup_for_source(self, source: SourceBean):
        """
        Initializes or re-initializes all source-specific components.
        This method is idempotent and will only run if the source code or its
        underlying configuration has changed since the last operation.
        NOTE: This method is not thread-safe by itself; it must be called
        from within a locked context.
        """
        config_fingerprint = hashlib.sha1(
            json.dumps(source.model_dump(), sort_keys=True).encode()
        ).hexdigest()

        if self._current_source_code == source.code and self._config_fingerprint == config_fingerprint:
            return

        logging.info(f"Setting up Confluence components for source '{source.code}'.")

        # 1. Build configuration for the source
        config = build_confluence_config(source)
        self.config = config # Store config early

        # 2. Initialize or confirm the client
        # If no client was injected, create one now.
        if self.client is None:
            logging.info("No client injected. Creating a new ConfluenceClient.")
            self.client = self._create_real_confluence_client(source, config)
        else:
            logging.info(f"Using pre-injected client of type: {type(self.client).__name__}")

        base_url = self.client.url

        # 3. Initialize CircuitBreaker
        circuit_breaker = CircuitBreaker(
            fail_max=config.performance.circuit_breaker_threshold,
            reset_timeout=config.performance.circuit_breaker_timeout_seconds
        )

        # 4. Inject dependencies into specialized components
        cql_search = CqlSearch(self.client, circuit_breaker)
        self.downloader = ContentDownloader(self.client)
        self.space_processor = SpaceProcessor(self.client, cql_search, base_url)

        # 5. Update the state trackers
        self._current_source_code = source.code
        self._config_fingerprint = config_fingerprint
        logging.info(f"Confluence components for source '{source.code}' are ready.")

    def _ensure_components_are_ready(self) -> None:
        """Asserts that all required components have been initialized."""
        assert self.config is not None, "Configuration must be initialized."
        assert self.client is not None, "Confluence client must be initialized."
        assert self.downloader is not None, "Content downloader must be initialized."
        assert self.space_processor is not None, "Space processor must be initialized."

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Retrieve the list of all documents for a given source."""
        # The lock ensures that _setup_for_source is called safely by only one thread at a time.
        with self._lock:
            try:
                # Setup components if they haven't been for this source
                self._setup_for_source(source)
                self._ensure_components_are_ready()

                spaces_to_process = self.config.basic.spaces
                if not spaces_to_process:
                    logging.warning(f"No spaces are configured for source '{source.code}'. Returning empty list.")
                    return []

                logging.info(f"Processing {len(spaces_to_process)} configured space(s) for source '{source.code}'.")
                all_documents = []
                for space_key in spaces_to_process:
                    try:
                        # The space_processor uses the (potentially mocked) client
                        processed_docs = self.space_processor.process(space_key, source, self.config)
                        all_documents.extend(processed_docs)
                    except Exception as e:
                        logging.error(f"Failed to process space '{space_key}' for source '{source.code}': {e}", exc_info=True)

                logging.info(f"Found {len(all_documents)} documents in total for source '{source.code}'.")
                return all_documents

            except Exception as e:
                logging.error(f"Failed to retrieve document list for source '{source.code}': {e}", exc_info=True)
                raise ConfluenceClientException(f"Unable to get Confluence document list: {e}", original_exception=e, resource=source.code) from e

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Download the content of a specific document."""
        with self._lock:
            try:
                self._setup_for_source(source)
                self._ensure_components_are_ready()

                item_id = DocumentIdFormatter.parse_document_id(document.id)["confluence_id"]
                is_attachment = DocumentIdFormatter.is_attachment_id(item_id)
                is_drawio_diagram = DocumentIdFormatter.is_drawio_diagram_id(item_id)

                metadata = {
                    Metadata.DOCUMENT_ID: document.id,
                    Metadata.DOCUMENT_NAME: document.name,
                    "source_path": document.path,
                    "modificationDate": document.modification_time.isoformat(),
                }

                # The downloader uses the (potentially mocked) client
                if is_attachment:
                    logging.info(f"Downloading attachment: {document.name} (ID: {item_id})")
                    download_metadata = self.downloader.download_attachment(item_id, document, output_path, self.config)
                elif is_drawio_diagram:
                    logging.info(f"Downloading Draw.io diagram: {document.name} (ID: {item_id})")
                    download_metadata = self.downloader.download_drawio_diagram(item_id, document, output_path, self.config)
                else:
                    logging.info(f"Downloading page: {document.name} (ID: {item_id})")
                    download_metadata = self.downloader.download_page(item_id, document, output_path, self.config)

                metadata.update(download_metadata)
                logging.info(f"Successfully downloaded document {document.id}. Location: {metadata.get(Metadata.LOCATION)}")
                return metadata

            except Exception as e:
                logging.error(f"Failed to download document {document.id}: {e}", exc_info=True)
                raise ConfluenceClientException(f"Failed to download Confluence document {document.id}", original_exception=e, resource=document.id) from e

    def _create_real_confluence_client(self, source: SourceBean, config: ConfluenceConfig) -> ConfluenceClient:
        """
        Creates and returns a REAL Confluence client instance.
        This method is only called when a client is not injected.
        """
        # We need this local import to avoid circular dependencies at the top level
        from .client.confluence_credentials import ConfluenceCredentials

        logging.info(f"Creating REAL Confluence client for source: {source.code}")

        auth_mode = config.auth.confluence_auth_mode
        creds_dict = None

        if auth_mode == "perimeter":
            logging.info("Using 'perimeter' authentication mode for Confluence.")
            creds_dict = self.global_config.get_perimeter_confluence_credentials(source.perimeter_code)
            if not creds_dict:
                logging.warning(
                    f"Perimeter mode configured, but no secret found for '{source.perimeter_code}'. Falling back to global."
                )

        if creds_dict is None:
            logging.info("Using 'global' authentication mode for Confluence.")
            creds_dict = self.global_config.get_global_confluence_credentials()

        if not creds_dict:
            raise ConfluenceAuthenticationError(
                "No Confluence credentials found. Ensure secrets are configured.",
                resource=source.code
            )

        try:
            confluence_creds = ConfluenceCredentials.from_secret_dict(creds_dict)
        except ValueError as e:
            raise ConfluenceConfigurationError(
                message=f"Confluence credentials secret is improperly formatted: {e}",
                resource=source.code, original_exception=e
            ) from e

        return ConfluenceClient(credentials=confluence_creds, config=config)
