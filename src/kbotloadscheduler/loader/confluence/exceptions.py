"""
Specialized exceptions for the Confluence loader.

This module provides a self-contained set of exceptions for handling
errors specific to the Confluence loader's operations, such as API
interactions and configuration issues.
"""
from __future__ import annotations

from typing import Any, Optional

import requests

# This file is now self-contained and has NO relative imports.

__all__ = [
    "ConfluenceException",  # Renamed from ConfluenceLoaderException to match original base
    "ConfluenceClientException",
    "ConfluenceAuthenticationError",
    "ConfluencePermissionError",
    "ConfluenceNotFoundError",
    "ConfluenceRateLimitError",
    "ConfluenceTimeoutError",
    "ConfluenceConfigurationError",
]


class ConfluenceException(Exception):
    """Base exception for all Confluence loader errors."""
    def __init__(self, message: str, *, original_exception: Optional[Exception] = None, **kwargs: Any):
        # The **kwargs is here for compatibility with the old LoaderException signature
        super().__init__(message)
        self.message = message
        self.original_exception = original_exception
        self.context = kwargs.get('context', {})
        self.resource = kwargs.get('resource')
        self.operation = kwargs.get('operation')
        # For compatibility with old is_retryable/is_critical logic
        self.is_critical = kwargs.get('is_critical', True)
        self.is_retryable = kwargs.get('is_retryable', False)

    def __str__(self) -> str:
        if self.original_exception:
            return f"{self.message} (Caused by: {type(self.original_exception).__name__}: {self.original_exception})"
        return self.message

    def add_context(self, key: str, value: Any) -> None:
        """Adds a key-value pair to the exception's context dictionary."""
        self.context[key] = value

    def get_context(self, key: Optional[str] = None, default: Any = None) -> Any:
        """Retrieves a value from the context or the entire context."""
        if key is None:
            return self.context.copy()
        return self.context.get(key, default)


class ConfluenceConfigurationError(ConfluenceException):
    """Indicates an error in the Confluence loader configuration."""
    def __init__(self, message: str, *, config_key: Optional[str] = None, **kwargs: Any):
        super().__init__(message, is_critical=True, is_retryable=False, **kwargs)
        if config_key:
            self.add_context("config_key", config_key)


class ConfluenceClientException(ConfluenceException):
    """Represents a generic client-side error during an API call to Confluence."""
    def __init__(
        self,
        message: str,
        *,
        status_code: Optional[int] = None,
        response: Optional[requests.Response] = None,
        **kwargs: Any,
    ):
        super().__init__(message, **kwargs)
        self.response = response
        self.status_code = status_code if status_code is not None else (response.status_code if response else None)
        if self.response and not self.resource:
            self.resource = self.response.url

    @classmethod
    def from_http_error(
        cls,
        response: requests.Response,
        message: Optional[str] = None,
        **kwargs: Any,
    ) -> ConfluenceClientException:
        """Creates a specialized ConfluenceClientException from an HTTP error response."""
        status_code = response.status_code
        url = response.url
        msg = message or f"Confluence API call failed for {url} with status {status_code}"

        # Map status codes to specific exception classes
        exception_map = {
            401: ConfluenceAuthenticationError,
            403: ConfluencePermissionError,
            404: ConfluenceNotFoundError,
            429: ConfluenceRateLimitError,
            408: ConfluenceTimeoutError,
            504: ConfluenceTimeoutError,
        }
        exception_class = exception_map.get(status_code, cls)

        # Ensure resource is set for the new instance
        kwargs.setdefault('resource', url)

        return exception_class(
            msg,
            status_code=status_code,
            response=response,
            **kwargs,
        )


class ConfluenceAuthenticationError(ConfluenceClientException):
    """Raised for 401 Unauthorized errors."""
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message, is_critical=True, is_retryable=False, **kwargs)


class ConfluencePermissionError(ConfluenceClientException):
    """Raised for 403 Forbidden errors."""
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message, is_critical=True, is_retryable=False, **kwargs)


class ConfluenceNotFoundError(ConfluenceClientException):
    """Raised for 404 Not Found errors."""
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message, is_critical=False, is_retryable=False, **kwargs)


class ConfluenceRateLimitError(ConfluenceClientException):
    """Raised for 429 Too Many Requests errors."""
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message, is_critical=False, is_retryable=True, **kwargs)


class ConfluenceTimeoutError(ConfluenceClientException):
    """Raised for request timeout errors."""
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message, is_critical=False, is_retryable=True, **kwargs)
