"""
Post-search filtering logic for Confluence API results.

This module provides functions to apply additional filters (such as dates or labels)
on a set of already retrieved results, which is especially useful when the initial query
used a custom CQL.
"""

import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, List, Optional, Any

from ..config.schema import ConfluenceConfig


def apply_post_search_filters(results: List[Dict[str, Any]], config: ConfluenceConfig) -> List[Dict[str, Any]]:
    """
    Apply additional filters (date, labels) to the raw search results.

    This function is mainly intended to be used when `config.filtering.custom_cql` is set,
    since standard filters (date, labels) are not included in the custom CQL query.

    Args:
        results: The list of result dictionaries from the Confluence API.
        config: The configuration object containing filtering criteria.

    Returns:
        A new list of filtered results.
    """
    if not results:
        return []

    # If no custom CQL is used, assume filters have already been applied in the auto-generated CQL query.
    if not config.filtering.custom_cql:
        return results

    logging.info(f"Applying post-search filters to {len(results)} results...")

    filtered_results = []
    for result in results:
        if not isinstance(result, dict):
            continue

        if not _passes_label_filter(result, config) or not _passes_date_filter(result, config):
            continue

        filtered_results.append(result)

    logging.info(f"Post-CQL filtering complete: {len(results)} -> {len(filtered_results)} results kept.")
    return filtered_results


def get_result_labels(result: Dict[str, Any]) -> List[str]:
    """
    Utility function to extract the list of label names from a Confluence result object.

    Handles the nested structure of label data in API responses, which typically looks like:
    `{'metadata': {'labels': {'results': [{'name': 'label1'}, ...]}}}`

    Args:
        result: A dictionary representing a Confluence page or content object.

    Returns:
        A list of label names as strings. Returns an empty list if no labels are found.
    """
    labels_data = result.get("metadata", {}).get("labels", {}).get("results", [])
    return [label.get("name", "") for label in labels_data if label.get("name")]


def _passes_label_filter(result: Dict[str, Any], config: ConfluenceConfig) -> bool:
    """Check if a search result satisfies the label filtering criteria."""
    # If no label filter is defined in the config, the result always passes.
    if not config.filtering.labels and not config.filtering.exclude_labels:
        return True

    result_labels = get_result_labels(result)

    # Check excluded labels first.
    if config.filtering.exclude_labels and any(ex_label in result_labels for ex_label in config.filtering.exclude_labels):
        logging.debug("Result '%s' filtered out because it contains an excluded label.", result.get('title'))
        return False

    # If required labels are specified, the result must have at least one.
    if config.filtering.labels and not any(req_label in result_labels for req_label in config.filtering.labels):
        logging.debug("Result '%s' filtered out because it is missing a required label.", result.get('title'))
        return False

    return True


def _extract_modification_date(result: Dict[str, Any]) -> Optional[datetime]:
    """
    Extract and parse the last modification date from a search result.
    The date is returned as a timezone-aware datetime object.
    """
    # The date is often in the 'version' section for pages,
    # or at the root for search results.
    mod_date_str = result.get("version", {}).get("when") or result.get("lastModified")

    if not mod_date_str:
        return None

    try:
        # Handle ISO 8601 format with "Z" for Zulu/UTC
        if mod_date_str.endswith("Z"):
            # fromisoformat does not handle 'Z' directly before Python 3.11
            mod_date_str = mod_date_str[:-1] + "+00:00"

        # Handle milliseconds that may have more than 6 digits
        if '.' in mod_date_str:
            main_part, fractional_part = mod_date_str.split('.', 1)
            fractional_part = fractional_part.split('+')[0].split('-')[0]
            mod_date_str = mod_date_str.replace(f".{fractional_part}", f".{fractional_part[:6]}")

        return datetime.fromisoformat(mod_date_str)
    except (ValueError, TypeError) as e:
        logging.warning(f"Unable to parse modification date '{mod_date_str}': {e}. Date filter will be ignored for this item.")
        return None


def _passes_date_filter(result: Dict[str, Any], config: ConfluenceConfig) -> bool:
    """Check if a search result satisfies the date filtering criterion."""
    if config.filtering.last_modified_days is None:
        return True

    mod_date = _extract_modification_date(result)
    if not mod_date:
        # If no date is found, include by default to avoid losing documents.
        return True

    # Create the cutoff date, ensuring timezone awareness
    cutoff_date = datetime.now(UTC) - timedelta(days=config.filtering.last_modified_days)

    # Ensure the modification date is also timezone-aware for comparison
    if mod_date.tzinfo is None:
        # If the Confluence date has no timezone, assume UTC.
        mod_date = mod_date.replace(tzinfo=UTC)

    if mod_date < cutoff_date:
        logging.debug(
            "Result '%s' filtered out because its modification date (%s) is earlier than the cutoff date (%s).",
            result.get('title'), mod_date, cutoff_date
        )
        return False

    return True
