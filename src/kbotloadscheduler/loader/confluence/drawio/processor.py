"""
Dedicated processor for Draw.io diagram handling.

This module encapsulates the entire Draw.io processing workflow:
- Diagram detection
- Metadata extraction from diagram XML
- Generation of a final markdown representation
"""
import logging
import re
from typing import Dict, List, Optional

from kbotloadscheduler.bean.beans import DocumentBean
from kbotloadscheduler.loader.confluence.config.schema import ConfluenceConfig

from .detector import DrawioDetector, DrawioDiagram
from .extractor import DrawioExtractor, DrawioMetadata

logger = logging.getLogger(__name__)


class DrawioProcessor:
    """
    Encapsulates the complete Draw.io diagram processing workflow.
    This class is the single source of truth for converting a detected
    Draw.io diagram into its final, searchable markdown format.
    """

    def __init__(self, client):
        """
        Initializes the processor with necessary components.

        Args:
            client: The Confluence API client, used for fetching attachment content.
        """
        self.client = client
        self.detector = DrawioDetector()
        self.extractor = DrawioExtractor()

    @staticmethod
    def generate_basic_drawio_markdown(diagram: Optional[DrawioDiagram], document: DocumentBean, page_data: Dict) -> str:
        """
        DEPRECATED: Compatibility shim for legacy callers.
        """
        logger.warning("Call to deprecated 'generate_basic_drawio_markdown'. Please update callers to handle errors from 'generate_drawio_markdown' instead.")
        title = getattr(diagram, 'title', None) or document.name
        return DrawioProcessor(None)._generate_error_markdown(title, "Content could not be extracted (legacy fallback).")

    def generate_drawio_markdown(self, diagram_id: str, document: DocumentBean,
                                 page_data: Dict, config: ConfluenceConfig) -> str:
        """
        Generate markdown content for a specific Draw.io diagram.
        """
        try:
            page_storage_content = page_data.get('body', {}).get('storage', {}).get('value', '')
            page_view_content = page_data.get('body', {}).get('view', {}).get('value', '')
            page_content = page_storage_content or page_view_content
            page_id = page_data.get('id', '')

            if not page_content:
                logger.warning(f"Empty page content for {page_id}, cannot process Draw.io diagram {diagram_id}.")
                return self._generate_error_markdown(document.name, "The parent page content is empty.")

            diagrams = self.detector.detect_drawio_diagrams(page_content, page_id)
            target_diagram = self._find_diagram_by_id(diagrams, diagram_id)

            if not target_diagram:
                logger.warning(f"Draw.io diagram with ID {diagram_id} not found in page {page_id}.")
                return self._generate_error_markdown(document.name, f"Diagram with ID '{diagram_id}' was not found on the page.")

            xml_content = self._get_diagram_xml_content(target_diagram)
            if not xml_content:
                logger.warning(f"Could not retrieve XML content for diagram {diagram_id}.")
                return self._generate_error_markdown(document.name, "Could not retrieve the diagram's XML content.")

            metadata = self.extractor.extract_metadata(
                xml_content=xml_content,
                diagram_title=target_diagram.title or document.name
            )

            return self._generate_markdown(metadata, document, page_data)

        except Exception as e:
            logger.error(f"Critical error in generate_drawio_markdown for {diagram_id}: {e}", exc_info=True)
            return self._generate_error_markdown(document.name, f"An unexpected error occurred: {e}")

    def _get_diagram_xml_content(self, diagram: DrawioDiagram) -> Optional[str]:
        """
        Retrieves the XML content for a diagram, fetching from an attachment if necessary.
        """
        if diagram.xml_content:
            return diagram.xml_content

        if diagram.attachment_id:
            logger.info(f"Diagram {diagram.diagram_id} points to attachment ID {diagram.attachment_id}. Fetching content.")
            try:
                attachment_content = self.client.get_attachment_content(diagram.attachment_id)
                return attachment_content.decode('utf-8')
            except Exception as e:
                logger.error(f"Failed to fetch or decode attachment {diagram.attachment_id}: {e}")
                return None

        return None

    def _find_diagram_by_id(self, diagrams: List[DrawioDiagram], target_id: str) -> Optional[DrawioDiagram]:
        """Helper to find a diagram in a list by its unique ID."""
        return next((d for d in diagrams if d.diagram_id == target_id), None)

    # --- Markdown Generation (Internal Implementation) ---

    def _generate_error_markdown(self, title: str, reason: str) -> str:
        """Creates a standard, simple markdown document for processing errors."""
        return f"# {title}\n\n**Note:** This document could not be processed.\n\n*Reason: {reason}*"

    def _generate_markdown(self, metadata: DrawioMetadata, document: DocumentBean, page_data: Dict) -> str:
        """
        Generates the full markdown document for a Draw.io diagram from its metadata.
        """
        md_parts = []
        page_title = page_data.get('title', 'N/A')
        page_url = document.path
        last_modified = page_data.get('version', {}).get('when')

        md_parts.append(f"# {metadata.diagram_title or document.name}\n")
        md_parts.append(f"**Source Page**: [{page_title}]({page_url})")
        if last_modified:
            md_parts.append(f"**Last Modified**: {last_modified}")
        md_parts.append("\n---\n")

        md_parts.append(f"## Diagram Overview\n")
        md_parts.append(f"**Inferred Diagram Type**: `{metadata.diagram_type.capitalize()}`\n")

        if not metadata.shapes and not metadata.connections and metadata.all_text:
            md_parts.append("### Extracted Text Elements\n")
            md_parts.append(
                "_Note: This diagram was likely processed from a rendered image, so detailed shape and connection information is not available. The following text elements were found:_")
            for text in sorted(list(set(metadata.all_text))):
                if text:
                    md_parts.append(f"- `{text}`")
        else:
            self._generate_semantic_summary(metadata, md_parts)
            self._generate_detailed_elements(metadata, md_parts)

        return "\n".join(md_parts)

    def _generate_semantic_summary(self, metadata: DrawioMetadata, md_parts: List[str]):
        """Generates the semantic summary section from categorized elements."""
        md_parts.append("### Semantic Summary\n")
        has_content = False
        for category, items in metadata.semantic_elements.items():
            cleaned_items = [
                ' '.join(re.sub('<[^<]+?>', ' ', item).split())
                for item in items if item and ' '.join(re.sub('<[^<]+?>', ' ', item).split())
            ]
            if cleaned_items:
                has_content = True
                md_parts.append(f"**{category.replace('_', ' ').title()}:**\n")
                for item in sorted(list(set(cleaned_items))):
                    md_parts.append(f"- {item}")
                md_parts.append("")
        if not has_content:
            md_parts.append("No distinct semantic elements were identified.\n")

    def _generate_detailed_elements(self, metadata: DrawioMetadata, md_parts: List[str]):
        """Generates tables for all shapes and connections."""
        md_parts.append("\n---\n")
        md_parts.append("## Detailed Elements\n")

        shape_text_map = {
            shape.id: ' '.join(re.sub('<[^<]+?>', ' ', shape.text).split())
            for shape in metadata.shapes
        }

        if metadata.shapes:
            md_parts.append("### Shapes\n")
            md_parts.append("| ID | Type | Text |")
            md_parts.append("|---|---|---|")
            # // FIX: Use tuple-based sort key to group shapes with no ID last.
            for shape in sorted(metadata.shapes, key=lambda s: (s.id is None, s.id or "")):
                text_md = shape_text_map.get(shape.id, "")
                md_parts.append(f"| `{shape.id or 'N/A'}` | `{shape.shape_type}` | {text_md} |")
            md_parts.append("\n")

        if metadata.connections:
            md_parts.append("### Connections\n")
            md_parts.append("| Source | Target | Label |")
            md_parts.append("|---|---|---|")
            for conn in sorted(metadata.connections, key=lambda c: (c.source or "", c.target or "")):
                source_text = shape_text_map.get(conn.source, f"Shape `{conn.source}`")
                target_text = shape_text_map.get(conn.target, f"Shape `{conn.target}`")
                label_text = ' '.join(re.sub('<[^<]+?>', ' ', conn.text).split())
                md_parts.append(f"| {source_text} | {target_text} | {label_text} |")
