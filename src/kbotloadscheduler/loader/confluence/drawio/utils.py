"""
Utilities for handling Draw.io diagrams in Confluence.
"""
from dataclasses import dataclass
from kbotloadscheduler.loader.confluence.exceptions import ConfluenceException

# Draw.io types
DRAWIO_TYPE_MACRO = "macro"
DRAWIO_TYPE_ATTACHMENT = "attachment"
DRAWIO_TYPE_EMBEDDED = "embedded"


@dataclass
class DrawioIdComponents:
    """Components of a Draw.io diagram ID."""
    page_id: str
    diagram_type: str
    diagram_index: int


def parse_drawio_id(drawio_id: str) -> DrawioIdComponents:
    """
    Parse a Draw.io diagram ID to extract its components.

    Args:
        drawio_id: Diagram ID (e.g., "2392678238_macro_0")

    Returns:
        DrawioIdComponents: Structured components of the Draw.io ID

    Raises:
        ConfluenceException: If ID format is not recognized
    """
    try:
        if "_macro_" in drawio_id:
            parts = drawio_id.split("_macro_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_MACRO, int(parts[1]))
        elif "_att_" in drawio_id:
            parts = drawio_id.split("_att_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_ATTACHMENT, int(parts[1]))
        elif "_embedded_" in drawio_id:
            parts = drawio_id.split("_embedded_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_EMBEDDED, int(parts[1]))
        else:
            raise ValueError(f"Unrecognized Draw.io ID format: {drawio_id}")
    except (ValueError, IndexError) as e:
        raise ConfluenceException(f"Cannot parse Draw.io ID '{drawio_id}': {e}") from e


