"""
Draw.io diagram detector for Confluence pages.

This module analyzes the HTML content of Confluence pages to identify
Draw.io diagrams embedded via macros or attachments.
"""
import logging
import re
import base64
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DrawioDiagram:
    """Represents a Draw.io diagram detected in a Confluence page."""
    diagram_id: str
    diagram_type: str  # 'macro', 'attachment', 'embedded'
    title: Optional[str] = None
    macro_content: Optional[str] = None
    attachment_id: Optional[str] = None
    xml_content: Optional[str] = None
    page_context: Optional[str] = None


class DrawioDetector:
    """
    Detects Draw.io diagrams in the content of Confluence pages.
    """
    DRAWIO_MACRO_PATTERNS = [
        r'<div[^>]*data-macro-name="drawio"[^>]*>',
        r'<ac:structured-macro[^>]*ac:name="drawio"[^>]*>',
        r'<ac:structured-macro[^>]*ac:name="draw\.io"[^>]*>',
    ]

    XML_PATTERNS = [
        r'<mxGraphModel[^>]*>.*?</mxGraphModel>',
        r'<mxfile[^>]*>.*?</mxfile>',
    ]

    # Regex to find auto-generated PNG exports from Draw.io, often ending in a timestamp.
    DRAWIO_PNG_EXPORT_REGEX = re.compile(r'-\d{10,13}\.png\Z', re.IGNORECASE)
    # Keywords in a filename indicating it might be a Draw.io diagram or its export.
    DRAWIO_INDICATORS = ["diagram", "drawio", "draw.io"]

    def __init__(self):
        self.compiled_macro_patterns = [re.compile(p, re.IGNORECASE | re.DOTALL) for p in self.DRAWIO_MACRO_PATTERNS]
        self.compiled_xml_patterns = [re.compile(p, re.IGNORECASE | re.DOTALL) for p in self.XML_PATTERNS]

    def is_auto_export(self, filename: str, extract_as_docs: bool, include_png_exports: bool) -> bool:
        """
        Checks if a PNG is likely an auto-export from a Draw.io diagram.
        """
        # Logic is now simpler and uses the direct boolean arguments.
        if not extract_as_docs or include_png_exports:
            return False

        if not filename or not filename.lower().endswith('.png'):
            return False

        name_lower = filename.lower()
        has_keyword = any(indicator in name_lower for indicator in self.DRAWIO_INDICATORS)
        has_timestamp_suffix = self.DRAWIO_PNG_EXPORT_REGEX.search(filename) is not None

        return has_keyword or has_timestamp_suffix

    def detect_drawio_diagrams(self, page_content: str, page_id: str, attachments: List[Dict] = None) -> List[DrawioDiagram]:
        diagrams = []
        if page_content:
            diagrams.extend(self._detect_macro_diagrams(page_content, page_id))

        logger.info(f"Page {page_id}: {len(diagrams)} Draw.io diagrams detected")
        return diagrams

    def _detect_macro_diagrams(self, page_content: str, page_id: str) -> List[DrawioDiagram]:
        diagrams = []
        for pattern in self.compiled_macro_patterns:
            for i, match in enumerate(pattern.finditer(page_content)):
                diagram_id = f"{page_id}_macro_{i}"
                macro_start = match.start()

                # Determine macro type for correct end tag finding
                is_ac_macro = match.group(0).startswith('<ac:structured-macro')
                macro_end = self._find_macro_end(page_content, macro_start, is_ac_macro)
                macro_content = page_content[macro_start:macro_end]

                title, attachment_id, xml_content = self._extract_content_from_macro(macro_content)

                diagram = DrawioDiagram(
                    diagram_id=diagram_id,
                    diagram_type='macro',
                    title=title,
                    macro_content=macro_content,
                    attachment_id=attachment_id,
                    xml_content=xml_content,
                    page_context=self._extract_context_around_match(page_content, match)
                )
                diagrams.append(diagram)
        return diagrams

    def _find_macro_end(self, content: str, start_pos: int, is_ac_macro: bool) -> int:
        if is_ac_macro:
            end_tag = '</ac:structured-macro>'
            end_pos = content.find(end_tag, start_pos)
            return end_pos + len(end_tag) if end_pos != -1 else len(content)

        # Handle nested <div> tags
        open_tag = '<div'
        close_tag = '</div>'
        pos = start_pos + len(open_tag)
        nesting_level = 1

        while nesting_level > 0 and pos < len(content):
            next_open = content.find(open_tag, pos)
            next_close = content.find(close_tag, pos)

            if next_close == -1:
                return len(content)  # Malformed HTML

            if next_open != -1 and next_open < next_close:
                nesting_level += 1
                pos = content.find('>', next_open) + 1
            else:
                nesting_level -= 1
                pos = next_close + len(close_tag)
                if nesting_level == 0:
                    return pos
        return len(content)

    def _extract_content_from_macro(self, macro_content: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        title = None
        attachment_id = None
        xml_content = None

        # Case 1: Modern Confluence with hidden JSON data (your case)
        data_match = re.search(r'<div id="drawio-macro-data-[^"]+"[^>]*>(.*?)</div>', macro_content)
        if data_match:
            logger.info("Found modern Draw.io macro with hidden data div.")
            try:
                b64_data = data_match.group(1)
                json_data = json.loads(base64.b64decode(b64_data))
                attachment_id = json_data.get('attId')
                title = json_data.get('diagramName')
                logger.info(f"Extracted attachment ID: {attachment_id} and title: {title} from JSON.")
                return title, attachment_id, None
            except Exception as e:
                logger.error(f"Failed to parse Draw.io JSON data: {e}")

        # Case 2: Rendered SVG directly in macro
        svg_match = re.search(r'<svg[^>]*>.*?</svg>', macro_content, re.DOTALL | re.IGNORECASE)
        if svg_match:
            logger.info("Found embedded SVG in Draw.io macro.")
            xml_content = svg_match.group(0)
            return title, None, xml_content

        # Case 3: Old Confluence with CDATA
        cdata_match = re.search(r'<!\[CDATA\[(.*?)]]>', macro_content, re.DOTALL)
        if cdata_match:
            logger.info("Found CDATA in Draw.io macro.")
            xml_content = cdata_match.group(1).strip()
            # Try to get title from parameters in this case
            title_match = re.search(r'<ac:parameter ac:name="diagramName">(.*?)</ac:parameter>', macro_content)
            if title_match:
                title = title_match.group(1)
            return title, None, xml_content

        logger.warning("Could not extract any primary content (JSON, SVG, or CDATA) from Draw.io macro.")
        return None, None, None

    def _extract_context_around_match(self, content: str, match: re.Match, context_chars: int = 200) -> str:
        start = max(0, match.start() - context_chars)
        end = min(len(content), match.end() + context_chars)
        context = content[start:end]
        context = re.sub(r'<[^>]+>', ' ', context)
        context = re.sub(r'\s+', ' ', context).strip()
        return context
