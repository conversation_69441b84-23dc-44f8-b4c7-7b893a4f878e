import logging
import os
from functools import wraps
from typing import Any, Dict, Optional, <PERSON><PERSON>, NamedTuple, Callable, Iterator
from dataclasses import dataclass

from kbotloadscheduler.gcs import gcs_utils
from kbotloadscheduler.bean.beans import DocumentBean, Metadata
from kbotloadscheduler.loader.confluence.exceptions import ConfluenceException
from ..config.schema import ConfluenceConfig

from ..utils.file_utils import (
    create_safe_filename,
    create_unique_filename,
    clean_filename,
)
from ..utils.url_utils import (
    extract_page_id_from_attachment_path,
    extract_parent_page_url,
    extract_parent_page_url_from_page_data
)
from kbotloadscheduler.loader.confluence.drawio.utils import parse_drawio_id
from ..utils.metadata_utils import (
    build_attachment_metadata,
    build_page_metadata,
    build_drawio_metadata,
    EXPORT_FORMAT_PDF,
    EXPORT_FORMAT_MARKDOWN,
    EXPORT_FORMAT_HTML,
    FILE_EXT_PDF,
    FILE_EXT_MARKDOWN,
    FILE_EXT_HTML
)

from kbotloadscheduler.loader.confluence.drawio.processor import DrawioProcessor
from .markdown_converter import MarkdownConverter, create_markdown_converter


@dataclass(slots=True)
class ProcessedPageContent:
    content: str
    file_ext: str

class FileDestinationInfo(NamedTuple):
    unique_filename: str
    path: str
    conflict_resolved: bool

def _handle_download_errors(doc_type: str) -> Callable[..., Any]:
    """Decorator to handle common download exceptions, logging, and re-raising."""
    def decorator(func):
        @wraps(func)
        def wrapper(self, item_id: str, document: DocumentBean, *args, **kwargs):
            try:
                doc_name = getattr(document, 'name', 'N/A') if document else 'N/A'
                logging.info(f"Starting download of {doc_type} {item_id} ('{doc_name}')")
                return func(self, item_id, document, *args, **kwargs)
            except ConfluenceException:
                raise
            except Exception as e:
                error_message = f"Failed to download {doc_type} {item_id}: {e}"
                logging.error(error_message, exc_info=True)
                raise ConfluenceException(error_message) from e
        return wrapper
    return decorator

class ContentDownloader:
    """Downloads Confluence document content with configurable processors."""

    def __init__(self, client, markdown_converter: Optional[MarkdownConverter] = None):
        self.client = client
        self.drawio_processor = DrawioProcessor(client)
        self.markdown_converter = markdown_converter or create_markdown_converter(client, "default")

    # --- Public Methods ---

    @_handle_download_errors("page")
    def download_page(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        export_format = config.basic.export_format.lower()
        logging.info("Export format for page %s configured as: '%s'", item_id, export_format)

        if export_format in ["markdown", "md"]:
            return self._download_page_as_text(item_id, document, output_path, config, EXPORT_FORMAT_MARKDOWN)
        elif export_format == "pdf":
            return self._download_page_as_pdf(item_id, document, output_path, config)
        else: # Default or "html"
            return self._download_page_as_text(item_id, document, output_path, config, EXPORT_FORMAT_HTML)

    @_handle_download_errors("attachment")
    def download_attachment(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        attachment_info = self._validate_and_extract_attachment_info(item_id, document)
        dest_info = self._prepare_destination_file(
            document.name, item_id, output_path, config.file_processing.duplicate_filename_strategy, use_safe_filename=True
        )

        attachment_api_id = item_id.replace("att", "")
        logging.info(f"Streaming attachment content for API ID: {attachment_api_id}")

        # 1. Get the stream iterator from the client
        content_stream = self.client.stream_attachment_content(attachment_id=attachment_api_id)

        # 2. Write the stream to GCS and get the final file size
        file_size = self._save_stream_and_verify_gcs_write(dest_info.path, content_stream)
        if file_size == 0:
            logging.warning(f"Attachment {attachment_api_id} resulted in an empty file (0 bytes).")

        metadata = build_attachment_metadata(
            dest_info.path, file_size, document.name,
            {'unique_filename': dest_info.unique_filename, 'conflict_resolved': dest_info.conflict_resolved},
            attachment_info, Metadata.LOCATION
        )
        logging.info(f"Attachment '{document.name}' streamed and verified to {dest_info.path}")
        return metadata

    @_handle_download_errors("drawio_diagram")
    def download_drawio_diagram(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        drawio_id = parse_drawio_id(item_id)
        page_id = drawio_id.page_id
        logging.info(f"Processing Draw.io diagram {item_id} from page {page_id}")

        page_data = self.client.get_page_content(page_id, expand="body.view,body.storage,version")
        markdown_content = self.drawio_processor.generate_drawio_markdown(
            diagram_id=item_id, document=document, page_data=page_data, config=config
        )
        content_bytes = markdown_content.encode('utf-8')

        base_filename = f"{clean_filename(document.name)}.{FILE_EXT_MARKDOWN}"
        dest_info = self._prepare_destination_file(
            base_filename, item_id, output_path, config.file_processing.duplicate_filename_strategy
        )

        content_stream = iter([content_bytes])
        self._save_stream_and_verify_gcs_write(dest_info.path, content_stream)

        parent_page_url = extract_parent_page_url_from_page_data(page_data, page_id, self.client)
        metadata = build_drawio_metadata(
            destination_path=dest_info.path,
            content_size=len(content_bytes),
            original_filename=base_filename,
            unique_filename=dest_info.unique_filename,
            parent_page_url=parent_page_url,
            parent_page_id=page_id,
            location_key=Metadata.LOCATION,
            content=markdown_content if config.basic.include_content_in_metadata else None,
            include_content=config.basic.include_content_in_metadata
        )
        logging.info(f"Draw.io diagram downloaded successfully: {dest_info.path}")
        return metadata

    # --- Private/Internal Helpers ---

    def _download_page_as_text(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig, export_format: str) -> Dict[str, Any]:
        processed = self._get_processed_page_content(item_id, config, export_format)
        content_bytes = processed.content.encode('utf-8')

        base_filename = f"{clean_filename(document.name)}.{processed.file_ext}"
        dest_info = self._prepare_destination_file(
            base_filename, item_id, output_path, config.file_processing.duplicate_filename_strategy
        )

        content_stream = iter([content_bytes])
        self._save_stream_and_verify_gcs_write(dest_info.path, content_stream)

        return build_page_metadata(
            destination_path=dest_info.path,
            content_size=len(content_bytes),
            export_format=export_format,
            location_key=Metadata.LOCATION,
            content=processed.content if config.basic.include_content_in_metadata else None,
            include_content=config.basic.include_content_in_metadata
        )

    def _download_page_as_pdf(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        base_filename = f"{clean_filename(document.name)}.{FILE_EXT_PDF}"
        dest_info = self._prepare_destination_file(
            base_filename, item_id, output_path, config.file_processing.duplicate_filename_strategy
        )

        logging.info(f"Streaming PDF export for page {item_id} to {dest_info.path}")
        pdf_stream = self.client.stream_page_as_pdf(item_id)
        content_size = self._save_stream_and_verify_gcs_write(dest_info.path, pdf_stream)

        return build_page_metadata(
            destination_path=dest_info.path,
            content_size=content_size,
            export_format=EXPORT_FORMAT_PDF,
            location_key=Metadata.LOCATION,
            content=None,
            include_content=config.basic.include_content_in_metadata
        )

    def _get_processed_page_content(self, item_id: str, config: ConfluenceConfig, export_format: str) -> ProcessedPageContent:
        page_data = self.client.get_page_content(item_id, expand="body.view,body.storage,version")
        html_content = page_data.get('body', {}).get('storage', {}).get('value', '')

        if not html_content:
            # Add format context to maintain similar logging behavior
            logging.warning(f"HTML content is empty for page {item_id} (export format: {export_format}). An empty file will be created.")
            file_ext = FILE_EXT_MARKDOWN if export_format == EXPORT_FORMAT_MARKDOWN else FILE_EXT_HTML
            return ProcessedPageContent("", file_ext)

        if export_format == EXPORT_FORMAT_MARKDOWN:
            final_content = self.markdown_converter(html_content, page_id=item_id, config=config)
            return ProcessedPageContent(final_content, FILE_EXT_MARKDOWN)

        return ProcessedPageContent(html_content, FILE_EXT_HTML)

    def _prepare_destination_file(self, base_filename: str, item_id: str, output_path: str, strategy: str, use_safe_filename: bool = False) -> FileDestinationInfo:
        """Create a unique destination filename and path, handling conflicts according to strategy."""
        initial_filename = create_safe_filename(base_filename, item_id) if use_safe_filename else base_filename
        unique_filename = create_unique_filename(initial_filename, item_id, output_path, strategy)

        conflict_resolved = unique_filename != initial_filename

        if conflict_resolved:
            logging.info(f"Filename conflict resolved: '{base_filename}' -> '{unique_filename}' (strategy: {strategy})")

        destination_path = os.path.join(output_path, unique_filename)
        return FileDestinationInfo(unique_filename, destination_path, conflict_resolved)

    def _validate_and_extract_attachment_info(self, item_id: str, document: DocumentBean) -> Dict[str, Any]:
        page_id = extract_page_id_from_attachment_path(document.path, self.client)
        if not page_id:
            raise ConfluenceException(f"Cannot extract page_id from attachment path: {document.path}")
        logging.debug(f"Extracted page ID: {page_id} for attachment {item_id} ('{document.name}')")
        parent_page_url = extract_parent_page_url(document.path, page_id, self.client)
        return {'page_id': page_id, 'parent_page_url': parent_page_url}

    def _save_stream_and_verify_gcs_write(self, destination_path: str, content_iterator: Iterator[bytes]) -> int:
        """
        Writes content from a stream iterator to GCS and verifies the file exists.
        NOTE: Requires a gcs_utils function that can handle iterators.
        """
        logging.info(f"Writing stream to GCS: {destination_path}")

        # This function must be implemented in your gcs_utils module.
        # It should iterate through the chunks and write them, returning the total bytes.
        bytes_written = gcs_utils.create_file_from_stream(destination_path, content_iterator)

        logging.info(f"Wrote {bytes_written} bytes to {destination_path}")

        if not gcs_utils.exists_file_gcs(destination_path):
            raise ConfluenceException(f"GCS write verification failed. File {destination_path} not found after write operation.")

        logging.debug(f"File created and verified successfully in GCS: {destination_path}")
        return bytes_written
