"""
URL builder for Confluence resources.

This module provides a comprehensive URL builder for various Confluence
resources including pages, attachments, and spaces.
"""
from __future__ import annotations
import logging
import urllib.parse
from typing import Optional, Union

logger = logging.getLogger(__name__)


class ConfluenceUrlBuilder:
    """
    Builds URLs for various Confluence resources with proper encoding and validation.

    Supports:
    - Page URLs by space key and title
    - Page URLs by ID
    - Attachment URLs
    - Space URLs
    - Search URLs
    """

    def __init__(self, base_url: str):
        """
        Initialize the URL builder.

        Args:
            base_url: Base URL of the Confluence instance (without trailing slash)

        Raises:
            ValueError: If base_url is invalid
        """
        self.base_url = self._validate_and_clean_base_url(base_url)

    def build_page_url(self, space_key: str, page_title: str) -> str:
        """
        Build a page URL using space key and page title.

        Args:
            space_key: The space key (e.g., "DEV", "DOCS")
            page_title: The page title

        Returns:
            Complete URL to the page

        Raises:
            ValueError: If space_key or page_title is invalid
        """
        if not space_key or not space_key.strip():
            raise ValueError("Space key cannot be empty")
        if not page_title or not page_title.strip():
            raise ValueError("Page title cannot be empty")

        # Properly encode the page title for URL use
        encoded_title = urllib.parse.quote(page_title.strip(), safe='')
        clean_space_key = space_key.strip()

        return f"{self.base_url}/display/{clean_space_key}/{encoded_title}"

    def build_page_url_by_id(self, page_id: Union[str, int]) -> str:
        """
        Build a page URL using page ID.

        Args:
            page_id: The numeric page ID, as a string or integer

        Returns:
            Complete URL to the page

        Raises:
            ValueError: If page_id is invalid
        """
        if not page_id or not str(page_id).strip():
            raise ValueError("Page ID cannot be empty")

        clean_page_id = str(page_id).strip()
        return f"{self.base_url}/pages/viewpage.action?pageId={clean_page_id}"

    def build_attachment_url(
        self, page_id: Union[str, int], attachment_name: str
    ) -> str:
        """
        Build a download URL for an attachment.

        Args:
            page_id: The page ID containing the attachment, as a string or integer
            attachment_name: The name of the attachment file

        Returns:
            Complete URL to download the attachment

        Raises:
            ValueError: If parameters are invalid
        """
        if not page_id or not str(page_id).strip():
            raise ValueError("Page ID cannot be empty")
        if not attachment_name or not attachment_name.strip():
            raise ValueError("Attachment name cannot be empty")

        # Properly encode the attachment name
        encoded_name = urllib.parse.quote(attachment_name.strip(), safe='')
        clean_page_id = str(page_id).strip()

        return f"{self.base_url}/download/attachments/{clean_page_id}/{encoded_name}"

    def build_space_url(self, space_key: str) -> str:
        """
        Build a URL to a space homepage.

        Args:
            space_key: The space key

        Returns:
            Complete URL to the space

        Raises:
            ValueError: If space_key is invalid
        """
        if not space_key or not space_key.strip():
            raise ValueError("Space key cannot be empty")

        clean_space_key = space_key.strip()
        return f"{self.base_url}/display/{clean_space_key}/"

    def build_search_url(self, query: str, space_key: Optional[str] = None) -> str:
        """
        Build a search URL.

        Args:
            query: Search query
            space_key: Optional space key to limit search

        Returns:
            Complete search URL

        Raises:
            ValueError: If query is invalid
        """
        if not query or not query.strip():
            raise ValueError("Search query cannot be empty")

        # Build query parameters
        params = {'queryString': query.strip()}
        if space_key and space_key.strip():
            params['where'] = f'space={space_key.strip()}'

        query_string = urllib.parse.urlencode(params)
        return f"{self.base_url}/dosearchsite.action?{query_string}"

    def _validate_and_clean_base_url(self, base_url: str) -> str:
        """
        Validate and clean the base URL.
        Args:
            base_url: The base URL to validate

        Returns:
            Cleaned base URL without trailing slash

        Raises:
            ValueError: If base_url is invalid
        """
        if not base_url or not base_url.strip():
            raise ValueError("Base URL cannot be empty")

        clean_url = base_url.strip()

        # Basic URL validation
        if not (clean_url.startswith('http://') or clean_url.startswith('https://')):
            raise ValueError("Base URL must start with http:// or https://")
        # Reject URLs that are just protocol (e.g., 'http://')
        if clean_url in ("http://", "https://"):
            raise ValueError("Base URL must not be just protocol")

        # Remove trailing slash
        if clean_url.endswith('/'):
            clean_url = clean_url[:-1]

        return clean_url

    def is_confluence_url(self, url: str) -> bool:
        """
        Check if a URL belongs to this Confluence instance.

        Args:
            url: URL to check

        Returns:
            True if the URL belongs to this instance
        """
        if not url:
            return False

        return url.startswith(self.base_url)

    def extract_page_id_from_url(self, url: str) -> Optional[str]:
        """
        Extract page ID from a Confluence page URL.

        Args:
            url: The Confluence page URL

        Returns:
            Page ID if found, None otherwise
        """
        if not url or not self.is_confluence_url(url):
            return None

        # Try to extract from viewpage.action URLs
        if 'viewpage.action' in url and 'pageId=' in url:
            try:
                parsed = urllib.parse.urlparse(url)
                params = urllib.parse.parse_qs(parsed.query, keep_blank_values=True)
                if 'pageId' in params:
                    page_ids = params['pageId']
                    if not page_ids or page_ids[0] is None:
                        return ""
                    return page_ids[0]
            except Exception as e:
                logger.warning(f"Error extracting page ID from URL {url}: {e}")

        return None
