"""
Factory for creating DocumentBean objects from raw Confluence data.
"""
import html
from datetime import UTC, datetime

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean

from ..utils.document_id_utils import DocumentIdFormatter
from .confluence_url_builder import ConfluenceUrl<PERSON>uilder


def create_page_bean(page_data: dict, source: SourceBean, url_builder: ConfluenceUrlBuilder) -> DocumentBean:
    """Create a DocumentBean for a Confluence page."""
    page_id = page_data.get("id", "unknown")
    raw_title = page_data.get("title", f"Page {page_id}")
    # Unescape only if there's a potential entity to avoid unnecessary processing.
    page_title = html.unescape(raw_title) if '&' in raw_title else raw_title

    # Use the URL builder, preferring the API-provided link.
    web_ui_link = page_data.get("_links", {}).get("webui")
    page_url = f"{url_builder.base_url}{web_ui_link}" if web_ui_link else url_builder.build_page_url_by_id(page_id)

    # Extract modification time from page version, with fallback to now().
    mod_time = datetime.now(UTC)  # Default value
    mod_time_str = page_data.get("version", {}).get("when")
    if mod_time_str:
        try:
            # Handle ISO 8601 format like '2024-05-20T10:00:00.000Z'.
            # .replace('Z', '+00:00') is needed for compatibility with Python < 3.11.
            mod_time = datetime.fromisoformat(mod_time_str.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            # Fallback if the timestamp format is unexpected.
            mod_time = datetime.now(UTC)

    doc_id = DocumentIdFormatter.create_page_id(source, page_id)
    return DocumentBean(
        id=doc_id,
        name=page_title,
        path=page_url,
        modification_time=mod_time
    )


def create_attachment_bean(att_data: dict, source: SourceBean, parent_page_id: str, url_builder: ConfluenceUrlBuilder) -> DocumentBean:
    """Create a DocumentBean for a Confluence attachment."""
    att_id = att_data.get("id", "unknown")
    raw_title = att_data.get("title", f"attachment_{att_id}")
    att_title = html.unescape(raw_title) if '&' in raw_title else raw_title

    # Use the URL builder, preferring the API-provided download link.
    download_link = att_data.get("_links", {}).get("download")
    att_url = f"{url_builder.base_url}{download_link}" if download_link else url_builder.build_attachment_url(parent_page_id, att_title)

    doc_id = DocumentIdFormatter.create_attachment_id(source, att_id)

    return DocumentBean(
        id=doc_id,
        name=att_title,
        path=att_url,
        modification_time=datetime.now(UTC)
    )


def create_drawio_bean(
    diagram_data: dict,
    source: SourceBean,
    page_id: str,
    page_webui_path: str | None,
    url_builder: ConfluenceUrlBuilder
) -> DocumentBean:
    """
    Create a DocumentBean for a Draw.io diagram.

    Args:
        diagram_data: Dictionary containing diagram information (id, title, type, attachment_id).
        source: SourceBean containing domain and source codes.
        page_id: The ID of the Confluence page containing the diagram.
        page_webui_path: The relative web UI link for the page, if available. May be None
        url_builder: The ConfluenceUrlBuilder instance.

    Returns:
        DocumentBean with a unique ID based on the diagram's ID.
    """
    diagram_id = diagram_data.get("diagram_id", "unknown")
    raw_title = diagram_data.get("title") or f"Diagram {diagram_data.get('diagram_type', 'Draw.io')}"
    # Unescape only if there's a potential entity to avoid unnecessary processing.
    diagram_title = html.unescape(raw_title) if '&' in raw_title else raw_title

    # Use the URL builder for constructing diagram URLs.
    if diagram_data.get("attachment_id"):
        # If the diagram is a standalone attachment, point to its download URL.
        diagram_url = url_builder.build_attachment_url(page_id, diagram_title)
    else:
        # Otherwise, point to the page containing the diagram.
        diagram_url = f"{url_builder.base_url}{page_webui_path}" if page_webui_path else url_builder.build_page_url_by_id(page_id)

    doc_id = DocumentIdFormatter.create_drawio_id(source, diagram_id)
    return DocumentBean(
        id=doc_id,
        name=diagram_title,
        path=diagram_url,
        modification_time=datetime.now(UTC)
    )
