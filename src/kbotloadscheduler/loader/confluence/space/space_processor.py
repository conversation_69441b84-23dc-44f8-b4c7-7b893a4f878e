"""
Orchestrates the discovery of content in a single Confluence space,
creating DocumentBeans that serve as "work orders" for the ContentDownloader.
"""
from __future__ import annotations

import re
from collections import defaultdict
import logging
import os
from dataclasses import dataclass
from datetime import UTC, datetime
from functools import lru_cache
from typing import Any, Callable, Generator, ParamSpec, Sequence, TypeVar

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.confluence.exceptions import (
    ConfluenceClientException,
    ConfluenceNotFoundError,
    ConfluenceTimeoutError,
)

from ..client.confluence_client import ConfluenceClient
from ..config.schema import ConfluenceConfig
from ..config import defaults
from ..processors import bean_factory
from ..processors.confluence_url_builder import ConfluenceUrlBuilder
from kbotloadscheduler.loader.confluence.drawio.detector import DrawioDetector, DrawioDiagram
from ..search.cql_search import CqlSearch

logger = logging.getLogger(__name__)

P = ParamSpec("P")
R = TypeVar("R")


@dataclass
class ProcessStats:
    """A summary of the content discovered and yielded during processing."""
    pages_found: int = 0
    pages_yielded: int = 0
    diagrams_yielded: int = 0
    attachments_yielded: int = 0
    pages_skipped_no_id: int = 0
    processing_start_time: datetime | None = None
    processing_end_time: datetime | None = None

    def increment_pages_yielded(self):
        """Increments the count of yielded pages."""
        self.pages_yielded += 1

    def increment_diagrams_yielded(self):
        """Increments the count of yielded diagrams."""
        self.diagrams_yielded += 1

    def increment_attachments_yielded(self):
        """Increments the count of yielded attachments."""
        self.attachments_yielded += 1


class SpaceProcessor:
    """
    Discovers and creates DocumentBeans for all content in a Confluence space.

    This processor is a generator that yields DocumentBean instances and, upon
    completion, returns a ProcessStats object with a summary of the run.

    Example Usage:
        processor = SpaceProcessor(...)
        generator = processor.process(space_key, source, config)
        for bean in generator:
            # Process each document bean
            ...
        stats = generator.close() # or handle StopIteration.value
        logger.info("Processing finished with stats: %s", stats)
    """
    DRAWIO_EXTENSION = "drawio"

    def __init__(self, client: ConfluenceClient, cql_search: CqlSearch, base_url: str):
        self.client = client
        self.cql_search = cql_search
        self.base_url = base_url
        self.drawio_detector = DrawioDetector()
        self.url_builder = ConfluenceUrlBuilder(self.base_url)

    # --------------------------------------------------------------------------
    # Public Entry Point
    # --------------------------------------------------------------------------

    def process(self, space_key: str, source: SourceBean, config: ConfluenceConfig) -> Generator[DocumentBean, None, ProcessStats]:
        """
        Discover and yield DocumentBeans, returning final stats upon completion.
        """
        logger.info("🚀 Starting optimized processing for space '%s'", space_key)
        stats = ProcessStats(processing_start_time=datetime.now(UTC))
        self._validate_config(config)

        normalized_extensions = self._get_normalized_extensions(frozenset(config.attachments.file_extensions or []))

        # This call should already be fetching body.storage from our previous discussion
        all_pages = self._fetch_all_pages(space_key, config, stats)
        if all_pages is None:
            stats.processing_end_time = datetime.now(UTC)
            return stats
        if not all_pages:
            logger.info("✅ No pages found in space '%s'. Processing complete.", space_key)
            stats.processing_end_time = datetime.now(UTC)
            return stats

        logger.info("Fetching content for %d pages in parallel...", len(all_pages))
        page_ids = [page['id'] for page in all_pages if page.get('id')]

        # This is the single, fast, parallel operation
        content_by_page_id = self._call_api_safely(
            self.client.get_content_for_pages, config, page_ids
        ) or {}

        logger.info("Successfully fetched content for %d pages.", len(content_by_page_id))

        # Enrich the original list with the fetched content
        enriched_pages = []
        for page in all_pages:
            page_id = page.get('id')
            if page_id in content_by_page_id and "error" not in content_by_page_id[page_id]:
                # Merge the full data (with body.storage) into our lightweight object
                page.update(content_by_page_id[page_id])
                enriched_pages.append(page)
            else:
                logger.warning("Could not retrieve content for page ID %s. It will be skipped.", page_id)

        # The rest of the processing uses the enriched list
        yield from self._yield_pages_and_diagrams(enriched_pages, source, config, stats, normalized_extensions)

        if config.attachments.include_attachments:
            if config.attachments.only_referenced_attachments:
                # This method now works perfectly because `enriched_pages` contains body.storage
                yield from self._yield_referenced_attachments_optimized(
                    enriched_pages, source, config, stats, normalized_extensions
                )
            else:
                # Keep the original "fast but noisy" bulk method for all attachments
                yield from self._yield_all_attachments_bulk(
                    all_pages, source, config, stats, normalized_extensions
                )

        stats.processing_end_time = datetime.now(UTC)
        logger.info(
            "✅ Finished processing space '%s'. Final Stats: %s",
            space_key, stats
        )
        return stats

    def _extract_referenced_attachment_names(self, page_content: str) -> set[str]:
        """
        Parses page storage format to find all referenced attachment filenames using a robust regex.
        Handles single/double quotes, whitespace, and is case-insensitive.
        """
        if not page_content:
            return set()

        # IMPROVED REGEX: Handles different quotes and extra whitespace.
        # It finds <ri:attachment ... ri:filename="..." ... />
        # and returns a list of tuples, e.g., [('"', 'file1.pdf'), ("'", 'image.png')]
        pattern = r'<ri:attachment\b[^>]*\bri:filename=(["\'])(.*?)\1'

        # We extract the second group (the filename) from each match.
        found_filenames = [match[1] for match in re.findall(pattern, page_content, re.IGNORECASE)]

        return set(found_filenames)

    def _yield_referenced_attachments_optimized(
        self,
        all_pages: Sequence[dict[str, Any]],
        source: SourceBean,
        config: ConfluenceConfig,
        stats: ProcessStats,
        normalized_extensions: frozenset[str]
    ) -> Generator[DocumentBean, None, None]:
        """
        Highly optimized strategy to fetch ONLY referenced attachments.
        1. Parses content (already fetched) to find referenced filenames.
        2. Bulk-fetches all attachments for pages with references.
        3. Filters in-memory by matching filenames.
        """
        logger.info("📎 Using optimized hybrid strategy to fetch only referenced attachments.")

        # Step 1: Parse all page content to find referenced filenames.
        # This is fast because `all_pages` already contains the content.
        referenced_filenames_by_page = defaultdict(set)
        for page in all_pages:
            page_id = page.get("id")
            # Assumes cql_search was modified to expand=body.storage
            content = page.get("body", {}).get("storage", {}).get("value", "")
            if page_id and content:
                # Confluence attachment matching is case-insensitive, so we normalize
                # both referenced filenames and attachment titles to lowercase for matching.
                referenced_filenames_by_page[page_id] = {
                    name.lower() for name in self._extract_referenced_attachment_names(content)
                }

        pages_with_references = [pid for pid, names in referenced_filenames_by_page.items() if names]
        if not pages_with_references:
            logger.info("No referenced attachments found across all pages.")
            return

        logger.info(f"Found attachment references on {len(pages_with_references)} pages. Fetching attachments...")

        # Step 2: Bulk-fetch all attachments for pages that have references.
        # This is one parallel API operation.
        attachments_by_page_id = self._call_api_safely(
            self.client.get_all_attachments_for_pages, config, pages_with_references
        )
        if not attachments_by_page_id:
            return  # API call failed or no attachments found

        # Step 3: In-memory filtering and yielding.
        logger.info("Filtering %d sets of attachments against in-memory references...", len(attachments_by_page_id))
        for page_id, attachments in attachments_by_page_id.items():
            expected_filenames = referenced_filenames_by_page.get(page_id, set())
            for att_data in attachments:
                # Perform the case-insensitive check
                if att_data.get("title", "").lower() in expected_filenames:
                    # These shared helper methods are defined once in the class, adhering to DRY.
                    if self._accept_attachment(att_data, config, normalized_extensions):
                        if bean := self._create_attachment_bean(att_data, source, page_id):
                            stats.increment_attachments_yielded()
                            yield bean

    def _yield_all_attachments_bulk(
        self,
        all_pages: Sequence[dict[str, Any]],
        source: SourceBean,
        config: ConfluenceConfig,
        stats: ProcessStats,
        normalized_extensions: frozenset[str]
    ) -> Generator[DocumentBean, None, None]:
        """
        Fetches all attachments for the given pages in bulk, without checking for references.
        This was the original 'else' block logic.
        """
        logger.info("Fetching all attachments for pages (including unreferenced).")
        page_ids = [page['id'] for page in all_pages if page.get('id')]
        ATTACHMENT_BATCH_SIZE = defaults.DEFAULT_ATTACHMENT_BATCH_SIZE

        for i in range(0, len(page_ids), ATTACHMENT_BATCH_SIZE):
            id_batch = page_ids[i:i + ATTACHMENT_BATCH_SIZE]
            if not id_batch:
                continue
            logger.debug(
                "Fetching attachments for page batch %d/%d (size: %d)",
                (i // ATTACHMENT_BATCH_SIZE) + 1,
                (len(page_ids) + ATTACHMENT_BATCH_SIZE - 1) // ATTACHMENT_BATCH_SIZE,
                len(id_batch)
            )
            # This is a new helper wrapping the existing bulk fetch logic
            yield from self._yield_attachments_for_pages(id_batch, source, config, stats, normalized_extensions)

    # --------------------------------------------------------------------------
    # Core Processing Phases
    # --------------------------------------------------------------------------

    def _fetch_all_pages(self, space_key: str, config: ConfluenceConfig, stats: ProcessStats) -> Sequence[dict[str, Any]] | None:
        """Phase 1: Fetch metadata for all pages and update stats."""
        logger.info("🔎 Discovering all pages in space '%s'...", space_key)
        all_pages = self._call_api_safely(self.cql_search.search_all_pages_in_space, config, space_key, config)

        if all_pages is None: # API call failed
            logger.error("API Error: Could not fetch page list for space '%s'. Aborting processing for this space.", space_key)
            return None

        if not all_pages:
            logger.info("⏹️ No pages found in space '%s'.", space_key)
            return []

        stats.pages_found = len(all_pages)
        logger.info("📊 Found %d total pages in space '%s'.", stats.pages_found, space_key)
        return all_pages

    def _yield_pages_and_diagrams(self, pages: Sequence[dict[str, Any]], source: SourceBean, config: ConfluenceConfig, stats: ProcessStats, normalized_extensions: frozenset[str]) -> Generator[DocumentBean, None, None]:
        """Phase 2: Yield beans for pages and their diagrams, updating stats."""
        for i, page_data in enumerate(pages, 1):
            page_id = page_data.get("id")
            if not page_id:
                logger.warning("Skipping page with missing ID: %s", page_data.get("title", "N/A"))
                stats.pages_skipped_no_id += 1
                if config.performance.fail_fast:
                    raise ValueError(f"Found page with no ID: {page_data.get('title', 'N/A')}")
                continue

            self._log_progress("Processing pages", i, stats.pages_found, page_data.get('title', 'Unknown'))

            if page_bean := self._create_page_bean(page_data, source):
                stats.increment_pages_yielded()
                yield page_bean

            if config.attachments.extract_drawio_as_documents:
                yield from self._yield_drawio_diagrams_for_page(page_data, source, config, stats, normalized_extensions)

    def _yield_attachments_for_pages(self, page_ids: list[str], source: SourceBean, config: ConfluenceConfig, stats: ProcessStats, normalized_extensions: frozenset[str]) -> Generator[DocumentBean, None, None]:
        """Phase 3: Fetch, filter, and yield beans for attachments, updating stats."""
        logger.info("📎 Fetching attachments for %d pages in parallel (workers=%d)...",
                    len(page_ids), config.performance.max_parallel_workers)

        attachments_by_page_id = self._call_api_safely(
            self.client.get_all_attachments_for_pages, config, page_ids
        )
        if not attachments_by_page_id:
            return # API call failed or returned no attachments

        logger.debug("⚙️ Processing %d sets of page attachments...", len(attachments_by_page_id))
        for page_id, attachments in attachments_by_page_id.items():
            for att_data in attachments:
                if self._accept_attachment(att_data, config, normalized_extensions):
                    if bean := self._create_attachment_bean(att_data, source, page_id):
                        stats.increment_attachments_yielded()
                        yield bean

    def _yield_drawio_diagrams_for_page(self, page_data: dict[str, Any], source: SourceBean, config: ConfluenceConfig, stats: ProcessStats, normalized_extensions: frozenset[str]) -> Generator[DocumentBean, None, None]:
        """Yields beans for Draw.io diagrams, handling errors and updating stats."""
        page_id = page_data["id"]

        # Fetch page content and its direct attachments for Draw.io detection.
        page_full_data = self._call_api_safely(
            self.client.get_page_content, config, page_id, expand="body.storage,children.attachment"
        )
        if not page_full_data:
            return  # API call failed, error already logged by helper

        page_content = page_full_data.get('body', {}).get('storage', {}).get('value', '')
        if not page_content:
            return

        attachments_raw = page_full_data.get('children', {}).get('attachment', {}).get('results', [])
        attachments = self.client._filter_current_attachments(attachments_raw)

        try:
            diagrams = self.drawio_detector.detect_drawio_diagrams(page_content, page_id, attachments)
            diagram_generator = self._create_beans_for_valid_diagrams(diagrams, page_data, source, normalized_extensions)
            for bean in diagram_generator:
                stats.increment_diagrams_yielded()
                yield bean
        except Exception as e:
            logger.error("Unexpected error detecting Draw.io diagrams for page %s: %s", page_id, e, exc_info=True)
            if config.performance.fail_fast:
                raise

    def _accept_attachment(self, att_data: dict[str, Any], config: ConfluenceConfig, normalized_extensions: frozenset[str]) -> bool:
        """Single predicate to determine if an attachment should be processed."""
        filename = att_data.get("title", "")
        if not self._is_extension_allowed(filename, normalized_extensions):
            logger.debug("Skipping attachment '%s': file extension not in allowlist.", filename)
            return False

        if self.drawio_detector.is_auto_export(
            filename=filename,
            extract_as_docs=config.attachments.extract_drawio_as_documents,
            include_png_exports=config.attachments.include_drawio_png_exports
        ):
            logger.debug("Skipping attachment '%s': identified as a Draw.io auto-export.", filename)
            return False

        return True

    def _is_extension_allowed(self, filename: str, normalized_extensions: frozenset[str]) -> bool:
        """Helper to check if a filename's extension is in the allowlist."""
        if not normalized_extensions or not filename:
            return False

        file_ext = os.path.splitext(filename.lower())[1]
        if not file_ext:
            return False

        return file_ext.lstrip('.') in normalized_extensions

    def _should_include_drawio_diagram(self, diagram: DrawioDiagram, normalized_extensions: frozenset[str]) -> bool:
        """Determines if a detected Draw.io diagram should be included as a document."""
        if not normalized_extensions:
            return False

        if diagram.diagram_type in ["inline", "macro", "embedded"]:
            return self.DRAWIO_EXTENSION in normalized_extensions
        if diagram.title:
            return self._is_extension_allowed(diagram.title, normalized_extensions)
        return self.DRAWIO_EXTENSION in normalized_extensions

    # --------------------------------------------------------------------------
    # Bean Creation Helpers
    # --------------------------------------------------------------------------

    def _create_page_bean(self, page_data: dict[str, Any], source: SourceBean) -> DocumentBean | None:
        """Creates a DocumentBean for a page, logging any errors."""
        try:
            return bean_factory.create_page_bean(page_data, source, self.url_builder)
        except Exception as e:
            logger.error("Failed to create bean for page %s: %s", page_data.get('id'), e, exc_info=True)
            return None

    def _create_attachment_bean(self, att_data: dict[str, Any], source: SourceBean, page_id: str) -> DocumentBean | None:
        """Creates a DocumentBean for an attachment, logging any errors."""
        try:
            return bean_factory.create_attachment_bean(att_data, source, page_id, self.url_builder)
        except Exception as e:
            logger.error("Failed to create bean for attachment '%s' on page %s: %s",
                         att_data.get('title'), page_id, e, exc_info=True)
            return None

    def _create_beans_for_valid_diagrams(self, diagrams: Sequence[DrawioDiagram], page_data: dict, source: SourceBean, normalized_extensions: frozenset[str]) -> Generator[DocumentBean, None, None]:
        """Filters diagrams and yields DocumentBeans for the valid ones."""
        for diagram in diagrams:
            if self._should_include_drawio_diagram(diagram, normalized_extensions):
                if bean := self._create_drawio_document_safe(diagram, page_data, source):
                    yield bean

    def _create_drawio_document_safe(self, diagram: DrawioDiagram, page_data: dict, source: SourceBean) -> DocumentBean | None:
        """Safely creates a DocumentBean for a Draw.io diagram."""
        try:
            diagram_data = {
                "diagram_id": diagram.diagram_id, "title": diagram.title,
                "diagram_type": diagram.diagram_type, "attachment_id": diagram.attachment_id,
                "xml_content": diagram.xml_content,
            }
            # Extract page details required by the bean factory.
            # Upstream processing ensures page_data always has an "id" at this point.
            page_id = page_data["id"]
            page_web_ui_link = page_data.get("_links", {}).get("webui")

            # The bean factory expects all diagram-specific data, including xml_content,
            # to be passed within the diagram_data dictionary.
            return bean_factory.create_drawio_bean(
                diagram_data=diagram_data,
                source=source,
                page_id=page_id,
                page_webui_path=page_web_ui_link,
                url_builder=self.url_builder,
            )
        except Exception as e:
            logger.error("Failed to create bean for diagram %s: %s", diagram.diagram_id, e, exc_info=True)
            return None

    # --------------------------------------------------------------------------
    # Utilities
    # --------------------------------------------------------------------------

    def _call_api_safely(
        self,
        api_callable: Callable[P, R],
        config: ConfluenceConfig,
        *args: P.args,
        **kwargs: P.kwargs,
    ) -> R | None:
        """
        Executes a Confluence API callable and handles common exceptions.
        Logs errors and returns None on failure, or re-raises if fail_fast is enabled.
        """
        try:
            return api_callable(*args, **kwargs)
        except (ConfluenceClientException, ConfluenceNotFoundError, ConfluenceTimeoutError) as e:
            logger.warning("API error during operation, skipping. Details: %s", e)
            if config.performance.fail_fast:
                raise
            return None
        except Exception as e:
            logger.error("Unexpected error during API call: %s", e, exc_info=True)
            if config.performance.fail_fast:
                raise
            return None

    @staticmethod
    @lru_cache(maxsize=1)
    def _get_normalized_extensions(allowed_extensions: frozenset[str]) -> frozenset[str]:
        """
        Normalizes and caches file extensions for fast lookups.

        Args:
            allowed_extensions: A frozenset of extensions to improve cache hit rate.
        """
        return frozenset(e.lower().lstrip(".") for e in allowed_extensions)

    @staticmethod
    def _validate_config(config: ConfluenceConfig) -> None:
        """
        Performs basic validation on the processor's configuration.
        This method should ONLY READ the config, not modify it.
        """
        if not isinstance(config, ConfluenceConfig):
            raise TypeError("config must be an instance of ConfluenceConfig")
        if config.basic.child_page_depth > 50:
            raise ValueError("Configuration 'child_page_depth' cannot exceed 50.")

    @staticmethod
    def _log_progress(action: str, index: int, total: int, page_title: str) -> None:
        """Logs processing progress at regular intervals."""
        if total > 0 and (index % 50 == 0 or index == 1 or index == total):
            logger.info("🔄 %s %d/%d: '%s' (%.1f%%)",
                        action, index, total, page_title, (index / total) * 100)
