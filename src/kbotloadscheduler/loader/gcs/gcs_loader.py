import os
from typing import List
from ..abstract_loader import AbstractLoader, LoaderException
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean, Metadata
from kbotloadscheduler.gcs import gcs_utils


class GcsLoader(AbstractLoader):
    """Loader pour google cloud storage"""

    def __init__(self):
        super().__init__("gcs")

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        bucket = str(source.get_expected_conf('bucket'))
        prefix = str(source.get_expected_conf('prefix'))
        # should also use sa : sa = source.get_expected_conf('sa')
        list_blobs = gcs_utils.list_gcs_blob(os.path.join(bucket, prefix))
        return [self.as_document_bean(source.domain_code, source.code, blob)
                for blob in list_blobs]

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        metadata = {}
        bucket = source.get_expected_conf('bucket')
        source_path = f'{bucket}/{document.name}'
        destination_path = f'{output_path}/{document.name}'
        metadata[Metadata.DOCUMENT_ID] = document.id
        metadata[Metadata.DOCUMENT_NAME] = document.name
        metadata[Metadata.SOURCE_URL] = source_path
        metadata[Metadata.LOCATION] = destination_path
        if gcs_utils.exists_file_gcs(source_path):
            source_blob = gcs_utils.get_gcs_blob(source_path)
            gcs_utils.copy_file_to_destination(source_blob, destination_path)
            metadata = {
                **metadata,
                Metadata.CREATION_TIME: source_blob.time_created,
                Metadata.MODIFICATION_TIME: source_blob.updated
            }
            return metadata
        else:
            raise LoaderException("File %s doesn't exists" % source_path)

    @staticmethod
    def as_document_bean(domain_code, source_code, blob):
        return DocumentBean(
            id='|'.join([domain_code, source_code, blob.name]),
            name=blob.name,
            path=os.path.join('gs://'+blob.bucket.name, blob.name),
            modification_time=blob.updated
        )
