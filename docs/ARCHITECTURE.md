# kbot-load-scheduler : Documentation d'Architecture Complète

## 1. Introduction et Vision

`kbot-load-scheduler` est un composant central de l'écosystème Knowledge Bot. Sa mission principale est d'orchestrer et d'automatiser le chargement, la synchronisation et la préparation des données provenant de diverses sources de connaissance (Confluence, SharePoint, GCS, sources spécifiques "Basic") pour les rendre exploitables par les systèmes d'indexation et d'embedding du Knowledge Bot.

Ce système vise à garantir que le Knowledge Bot dispose toujours d'informations à jour et pertinentes, en gérant de manière robuste et efficace le cycle de vie des documents. Il est conçu pour être flexible, extensible et résilient, capable de s'adapter à différents types de sources et de volumes de données.

**Objectifs clés :**

-   **Automatisation :** Réduire l'intervention manuelle dans le processus de mise à jour des connaissances.
-   **Fiabilité :** Assurer un chargement de données constant et résilient aux erreurs.
-   **Extensibilité :** Permettre l'intégration facile de nouvelles sources de données.
-   **Performance :** Optimiser le processus de chargement pour minimiser les délais de mise à disposition des informations.
-   **Traçabilité :** Fournir une visibilité claire sur les opérations de chargement.

## 2. Fonctionnalités Clés

-   **Gestion de sources multiples :**
    -   **Confluence :** Synchronisation avancée des pages, blogs, pièces jointes, avec gestion des versions et détection des changements.
    -   **SharePoint :** Accès et récupération de documents depuis des sites SharePoint.
    -   **Google Cloud Storage (GCS) :** Chargement de fichiers directement depuis des buckets GCS.
    -   **Basic Loader :** Un chargeur générique pour des sources spécifiques (ex: fiches de procédure Basic) nécessitant une logique d'extraction personnalisée.
-   **Orchestration via GCS :** Le processus de chargement est décomposé en étapes granulaires (lister, comparer, récupérer, traiter, embedder, supprimer), pilotées par un système de fichiers d'état sur GCS pour garantir la reprise sur erreur et la distribution des tâches.
-   **API RESTful :** Des endpoints pour déclencher, suivre les chargements et s'intégrer avec les autres services de l'écosystème (`kbot-back`, `kbot-embedding`).
-   **Sécurité et Authentification :**
    -   Gestion centralisée des secrets via Google Secret Manager (avec un fallback sur des fichiers locaux pour les tests).
    -   Authentification aux API via des tokens standards (Okapi JWT, Cloud Run ID tokens).
-   **Optimisations et Résilience :** Mécanismes de *retry* avec *backoff* exponentiel, et Circuit Breaker pour protéger les appels vers les services externes.
-   **Déploiement Cloud-Native :** Conçu pour s'exécuter comme un service Cloud Run, avec une planification via Cloud Scheduler pour déclencher les chargements périodiques par périmètre.

## 3. Architecture Générale

`kbot-load-scheduler` est une application FastAPI construite autour d'un système de dépendances injectées (`dependency_injector`). Elle expose une API REST pour interagir avec des services internes qui orchestrent le chargement des données.

```mermaid
%% ----------  CONFIG  ----------
%%{ init : {
        'theme'      : 'base',
        'themeVariables': {
            'fontFamily'  : 'Segoe UI, Roboto, Helvetica, Arial, sans-serif',
            'fontSize'    : '14px',
            'lineColor'   : '#B0BEC5',
            'background'  : '#121212'
        },
        'flowchart' : {
            'useMaxWidth'  : true,
            'htmlLabels'   : true,
            'curve'        : 'basis'
        }
}}%%

%% ----------  DIAGRAM  ----------
graph TD
    %% 1. Top-level orchestration  ---------------------------------
    GCP_Cloud_Scheduler["⏰ Google Cloud&nbsp;<br/>Scheduler"]
    GCP_Cloud_Run["⚙️ Google Cloud&nbsp;<br/>Run"]
    GCP_Cloud_Scheduler -->|"triggers"| GCP_Cloud_Run

    %% 2. Main container  -----------------------------------------
    subgraph KBOT_Load_Scheduler["<b>kbot-load-scheduler</b>"]
        direction TB

        API["📍 API Endpoints<br/>(FastAPI)"]

        subgraph Core["🧠 Core Logic"]
            SS["⚙️ ScheduleService"]
            SrcS["⚙️ SourcesService"]
            LS["⚙️ LoaderService"]
            DS["⚙️ DocumentService"]
            TFM["📂 TreatmentFileManager"]
        end

        subgraph Loaders["📥 Data Loaders"]
            AL["📤 AbstractLoader"]
            CL["📤 ConfluenceLoader"]
            SL["📤 SharePointLoader"]
            GL["📤 GCSLoader"]
            BL["📤 BasicLoader"]
        end

        CWS["🔑 ConfigWithSecret"]
        DI["🛠️ DI Container"]
    end
    GCP_Cloud_Run -->|"hosts"| KBOT_Load_Scheduler

    %% 3. External resources  -------------------------------------
    subgraph Cloud["☁️ Google Cloud Platform"]
        SM["🗄️ Secret Manager"]
        GCS["📦 Cloud Storage<br/>(work bucket)"]
    end

    subgraph KB_Eco["🤖 Knowledge Bot Ecosystem"]
        KBack["📡 kbot-back API"]
        KEmb["📡 kbot-embedding API"]
    end

    subgraph ExtSrc["🌐 External Data Sources"]
        Conf["📄 Confluence Instance"]
        SP["📄 SharePoint Instance"]
        Basic["📡 Basic Source API"]
        SrcBucket["📦 Source GCS Bucket"]
    end

    %% 4. Relationships  ------------------------------------------
    API --> SS & SrcS & LS & DS

    SS --> TFM & SrcS & LS & DS & KBack
    SrcS --> KBack & TFM
    LS --> AL & TFM
    DS --> KEmb & TFM
    TFM -.->|"read / write"| GCS

    AL --> CL & SL & GL & BL
    CL -->|"API calls"| Conf
    SL -->|"API calls"| SP
    GL -->|"read"| SrcBucket
    BL -->|"API calls"| Basic

    CWS -->|"read"| SM
    CWS -.->|"read (tests)"| Local["🔑 Local Secrets"]


    %% ----------  A11Y & STYLE OVERRIDES  ----------
    classDef cloud    fill:#1565C0,stroke:#90A4AE,stroke-width:2px,color:#FFFFFF
    classDef eco      fill:#7B1FA2,stroke:#90A4AE,stroke-width:2px,color:#FFFFFF
    classDef ext      fill:#2E7D32,stroke:#90A4AE,stroke-width:2px,color:#FFFFFF

    class GCP_Cloud_Scheduler,GCP_Cloud_Run,SM,GCS cloud
    class KBack,KEmb eco
    class Conf,SP,Basic,SrcBucket ext

    %% ---------- SUBGRAPH BACKGROUND COLORS  --------------
    style Cloud        fill:#1E3A5F,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style KB_Eco       fill:#3D2B56,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style ExtSrc       fill:#4A4A4A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style KBOT_Load_Scheduler fill:#2E4B6A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Core         fill:#3D2B56,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Loaders      fill:#4A4A4A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
```

### 4. Flux de Travail et d'Orchestration

1.  **Déclenchement :** `Cloud Scheduler` appelle périodiquement l'endpoint `POST /schedule/treatments/{perimeter}/launch`.
2.  **Initialisation du chargement :** Le `ScheduleService` interroge `kbot-back` (via `SourcesService`) pour récupérer la liste des sources à synchroniser. Pour chaque source, le `TreatmentFileManager` crée un fichier de tâche `getlist.json` sur GCS.
3.  **Traitement des Tâches :** Le `ScheduleService` identifie les tâches en attente sur GCS (fichiers `.json` sans fichier `.done` correspondant) et déclenche les appels API appropriés pour chaque étape :
    -   **Lister (`/loader/list`) :** Le `LoaderService` sélectionne le `Loader` approprié (`Confluence`, `SharePoint`, etc.) pour lister les documents de la source et écrit le résultat dans un fichier `list.json`.
    -   **Comparer (`/document/compare`) :** Le `DocumentService` compare le `list.json` avec les documents déjà présents dans la base vectorielle (via `kbot-embedding`) et génère les listes de documents à ajouter (`getdoc.json`) ou à supprimer (`removedoc.json`).
    -   **Récupérer (`/loader/document`) :** Pour chaque `getdoc.json`, le `Loader` approprié télécharge le contenu du document, le stocke sur GCS, et crée un fichier de métadonnées (`.metadata.json`).
    -   **Indexer et Supprimer (`/document/embedd`, `/document/remove`) :** Le `DocumentService` appelle `kbot-embedding` pour indexer les nouveaux documents ou supprimer les anciens.
4.  **Gestion d'état :** Chaque tâche est marquée comme `.inprogress`, puis `.done` (ou `.error`), ce qui assure la traçabilité et la reprise sur erreur.
5.  **Nettoyage :** Le `ScheduleService` purge périodiquement les anciens répertoires de traitement sur GCS.

```mermaid
sequenceDiagram
    participant CS as "⏰ Cloud Scheduler"
    participant KLS_API as "📍 kbot-load-scheduler API"
    participant SS as "⚙️ ScheduleService"
    participant SrcS as "⚙️ SourcesService"
    participant KBApi as "📡 KbotBackApi"
    participant TFM as "📂 TreatmentFileManager"
    participant LS as "⚙️ LoaderService"
    participant Loader as "📤 AbstractLoader (Confluence, SharePoint, etc.)"
    participant DS as "⚙️ DocumentService"
    participant KEApi as "📡 KbotEmbeddingApi"
    participant GCS as "📦 Google Cloud Storage (Working Bucket)"

    CS ->> KLS_API: "'POST /schedule/treatments/{perimeter}/launch'"
    KLS_API ->> SS: "'launch_treatments_for_perimeter(perimeter)'"
    SS ->> SS: "'__populate_tasks_to_execute__(perimeter)'"
    SS ->> TFM: "'get_load_dates(perimeter)'"
    TFM ->> GCS: "'List date directories'"
    GCS -->> TFM: "'List of load_date'"
    TFM -->> SS: "'List of load_date'"
    loop "'for each load_date'"
        SS ->> TFM: "'get_treatments_files(perimeter, load_date)'"
        TFM ->> GCS: "'List files in load_date dir'"
        GCS -->> TFM: "'Map of files and statuses'"
        TFM -->> SS: "'Map of files'"
    end
    SS ->> SS: "'Sorts tasks by priority (DOCS, REMOVE_DOC, GET_DOC, LIST, GET_LIST)'"

    alt "'No pending tasks AND execution time < MAX'"
        SS ->> SrcS: "'load_sources(perimeter)'"
        SrcS ->> KBApi: "'get_sources(perimeter)'"
        KBApi -->> SrcS: "'List[SourceBean]'"
        loop "'for each SourceBean'"
            SrcS ->> TFM: "'write_get_list(load_date, source)'"
            TFM ->> GCS: "'Create {load_date}/{domain}/{source}/getlist/getlist.json'"
            GCS -->> TFM: "'OK'"
            TFM -->> SrcS: "'OK'"
            SrcS ->> KBApi: "'set_source_done(source.id)'"
            KBApi -->> SrcS: "'OK'"
        end
        SrcS -->> SS: "'List of sources processed'"

        SS ->> TFM: "'purge_old_treatments_files(perimeter, now)'"
        TFM ->> GCS: "'Delete old date directories'"
        GCS -->> TFM: "'OK'"
        TFM -->> SS: "'OK'"
        SS -->> KLS_API: "'{\"status\": \"ok\"}'"
        KLS_API -->> CS: "'Response'"
    else "'For each task type in priority order'"
        SS ->> SS: "'__launch_single_treatment_type__(start_time, \"getlist\", LS.get_document_list, perimeter)'"
        loop "'for each getlist.json file'"
            SS ->> LS: "'get_document_list(perimeter, getlist_file_path)'"
            LS ->> TFM: "'set_in_progress(getlist_file_path)'"
            TFM ->> GCS: "'Create getlist.inprogress'"
            LS ->> TFM: "'read_get_list(getlist_file_path)'"
            TFM ->> GCS: "'Read getlist.json'"
            GCS -->> TFM: "'SourceBean data'"
            TFM -->> LS: "'SourceBean'"
            LS ->> Loader: "'get_document_list(SourceBean)'"
            Loader -->> LS: "'List[DocumentBean]'"
            LS ->> TFM: "'write_document_list(load_date, SourceBean, documents, \"list\")'"
            TFM ->> GCS: "'Create {load_date}/{domain}/{source}/list/list.json'"
            GCS -->> TFM: "'OK'"
            TFM -->> LS: "'Serialized documents'"
            LS ->> TFM: "'set_done(getlist_file_path)'"
            TFM ->> GCS: "'Create getlist.done, delete .inprogress'"
            LS -->> SS: "'List of serialized DocumentBeans'"
        end
        SS ->> SS: "'Log \"getlist\" treatments launched'"

        Note over SS: "Process continues similarly for LIST, GET_DOC, DOCS, REMOVE_DOC steps,\ncalling appropriate services (DS, LS) and APIs (KEApi).\nEach step reads input files from GCS and writes output files to GCS."

        SS -->> KLS_API: "'{\"status\": \"ok\"}'"
        KLS_API -->> CS: "'Response'"
    end
```

## 5. Composants Détaillés

### 5.1 API Endpoints (`src/kbotloadscheduler/route/`)

-   **`schedule_routes.py` :** Points d'entrée pour l'orchestration (`/schedule/treatments/...`).
-   **`sources_routes.py` :** Déclenche la création des tâches de chargement (`/sources/load/...`).
-   **`loader_routes.py` :** Endpoints génériques pour appeler les `Loaders` afin de lister et télécharger les documents. Le `LoaderManager` sélectionne automatiquement le bon chargeur en fonction du `src_type` de la source.
-   **`document_routes.py` :** Endpoints pour la comparaison, l'embedding et la suppression des documents.

Pour une documentation exhaustive des API, consultez la **[Référence des API](./API_REFERENCE.md)**.

### 5.2 Chargeurs de Données (`src/kbotloadscheduler/loader/`)

Le système utilise un `LoaderManager` pour sélectionner le chargeur approprié. Tous les chargeurs héritent d'une classe `AbstractLoader`.

-   **`ConfluenceLoader` :** Implémentation riche pour Confluence, gérant les pages, pièces jointes, et optimisations RAG.
-   **`SharePointLoader` :** Gère l'authentification spécifique à SharePoint et la navigation dans les bibliothèques de documents.
-   **`BasicLoader` :** Conçu pour des API spécifiques ("fiches de procédure Basic"), il effectue un pré-traitement et une conversion Markdown.
-   **`GCSLoader` :** Chargeur simple pour copier des fichiers depuis un bucket GCS source vers le bucket de travail.

### 5.3 Services (`src/kbotloadscheduler/service/`)

-   **`ScheduleService` :** Le cœur de l'orchestration, qui lit l'état sur GCS et déclenche les traitements.
-   **`SourcesService` :** Gère la récupération des configurations de sources depuis `kbot-back`.
-   **`LoaderService` :** Orchestre les appels aux `Loaders` via le `LoaderManager`.
-   **`DocumentService` :** Gère la logique de comparaison et les appels vers `kbot-embedding`.

### 5.4 Exemple de corps de requête
```json
{
  "source_type": "confluence",
  "collection": "mon-collection",
  "source_name": "source-1",
  "config": {
    "space": "VBT",
    "auth": {
      "type": "pat",
      "secret_path": "confluence-credentials"
    }
  }
}
```

## 6. Patterns Architecturaux

### 6.1 Abstract Factory Pattern
**Base**: `AbstractLoader` dans `src/kbotloadscheduler/loader/abstract_loader.py`
```python
@abstractmethod
def get_document_list(self, source: SourceBean) -> List[DocumentBean]
@abstractmethod
def get_document(self, source: SourceBean, document: DocumentBean, output_path: str)
```

**Implémentations concrètes:**
- `ConfluenceLoader` - Spécialisé Confluence
- `SharePointLoader` - Gestion Office365 docs
- `BasicLoader` - Fetching URL simple
- `GcsLoader` - Upload vers buckets

### 6.2 Dependency Injection Container
**Implémentation**: `src/kbotloadscheduler/dependency/container.py` avec Dependency Injector
```mermaid
classDiagram
    class Container {
        -config: Configuration
        -configWithSecret: ConfigWithSecret
        +loader_manager: LoaderManager
        +confluence_loader: ConfluenceLoader
        +sharepoint_loader: SharepointLoader
        +basic_loader: BasicLoader
        +gcs_loader: GcsLoader
    }
```

### 6.3 Configuration Environment Selector
```python
# Dynamique par environnement (container.py):
basic_auth_provider = providers.Selector(
    config.env,
    local=MockAuthProvider,
    tests=MockAuthProvider,
    dev=OkapiAuthProvider,
    ppr=OkapiAuthProvider,
    prd=OkapiAuthProvider
)
```

## 7. Sécurité et Authentification

### 7.1 Structure des secrets
Les secrets sont montés dans le conteneur en suivant cette arborescence.
```
conf/etc/secrets/
├── confluence-credentials/ (username + pat)
├── sharepoint-client-config/ (client-id + client-secret + tenant-id)
├── basic-client-id/ (token identifiants)
├── basic-client-secret/
└── gcs-service-account.json (service account key)
```

### 7.2 Types d'authentification par source
| Source | Type Auth | Credential Path | Validation |
|---|---|---|---|
| **Confluence** | Basic Auth + PAT | `confluence-credentials/secret` | Validée via API test |
| **SharePoint** | OAuth2 Client Credentials | `sharepoint-client-config/` | Tenant + client auth flow |
| **Basic** | HTTP Bearer Token | `basic-client-{secret}/secret` | Simple token validation |
| **GCS** | Service Account | `gcs-service-account.json` | IAM permissions check |

### 7.3 Gestion sécurisée via Container
La classe `ConfigWithSecret` centralise l'accès aux secrets avec une stratégie de fallback : fichiers locaux (`PATH_TO_SECRET_CONFIG`) d'abord, puis Google Secret Manager. Elle utilise `@lru_cache` pour mettre en cache les secrets et optimiser les performances.

```python
ConfigWithSecret = providers.Singleton(
    ConfigWithSecret,
    config=config
)
# L'injection de cette classe permet un accès sécurisé sans exposer les secrets dans le code.
```

## 8. Configuration Système

### 8.1 Variables d'environnement requises
```bash
ENV=local|tests|dev|ppr|prd
PATH_TO_SECRET_CONFIG=/chemin/secrets
KBOT_WORK_BUCKET_PREFIX=gs://bucket-[perimeter]

# Avec des valeurs par défaut définies:
KBOT_BACK_URL=https://kbot-back-api:8080
KBOT_EMBEDDING_URL=https://kbot-embedding:8081
```

### 8.2 Exemples de formats de secrets
**conf/etc/secrets/confluence-credentials/secret**:
```
username=kbot_user
pat=confluence_personal_access_token
url=https://confluence.example.com
```

**conf/etc/secrets/sharepoint-client-config/secret**:
```
client_id=spn_client_guid
client_secret=secret_value
tenant_id=tenant_guid_here
```

## 9. Déploiement et Tests

### 9.1 Déploiement Cloud Run (via GitLab CI)
Le déploiement est automatisé via le fichier `.gitlab-ci.yml`.
-   **Environnements** : `dev`, `ppr`, `prd` sont gérés avec des variables spécifiques.
-   **Configuration Cloud Run** : Le service est déployé avec un accès restreint (`INGRESS_TRAFFIC_INTERNAL_ONLY`), un compte de service dédié, et des secrets montés en tant que volumes dans `/etc/secrets/`.
-   **Planification Cloud Scheduler** : Des jobs sont créés pour chaque périmètre, appelant l'endpoint `/schedule/treatments/{perimeter}/launch` à intervalles réguliers.

### 9.2 Stratégie de Test
La stratégie de test est complète et automatisée via le `Makefile`.
-   **Tests unitaires** : `make unit-tests` lance les tests et génère un rapport de couverture.
-   **Tests d'intégration** : `make integration-tests-confluence` lance les tests spécifiques.
-   **Qualité du code** : `make flake` (style), `make bandit` (sécurité).
-   **Isolation des tests** : L'environnement `ENV=tests` utilise des mocks pour les API externes et des secrets dédiés, garantissant des tests reproductibles.
