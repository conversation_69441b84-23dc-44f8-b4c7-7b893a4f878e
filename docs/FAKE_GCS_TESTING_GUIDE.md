# Guide d'installation et de test multi-plateforme

Ce guide couvre l'installation et l'utilisation de `fake-gcs-server` et `gcsfs` pour tester le module Confluence loader sur macOS et Linux.

## � **Solution recommandée : Binaire directement (plus simple !)**

### Avantages du binaire vs Docker
- ✅ **Plus simple** : Pas besoin de <PERSON>er
- ✅ **Plus léger** : Consommation mémoire réduite
- ✅ **Plus rapide** : Démarrage instantané
- ✅ **Multiplateforme** : Support natif ARM64/x86_64
- ✅ **Portable** : Un seul fichier exécutable

## �🖥️ Compatibilité des plateformes

### ✅ Plateformes supportées
- **macOS** (Intel x86_64) - Support natif
- **macOS** (Apple Silicon M1/M2 ARM64) - Support natif
- **Linux** (x86_64) - Support natif
- **Linux** (ARM64/aarch64) - Support natif

## 📦 Installation simplifiée

### 1. Installation automatique avec le script

```bash
# Installation automatique (recommandée)
./scripts/fake-gcs-binary-manager.sh install

# Ou téléchargement manuel depuis GitHub
# Binaires disponibles : https://github.com/fsouza/fake-gcs-server/releases
```

### 2. Installation des dépendances Python

```bash
# Installation des packages Python
pip install google-cloud-storage pytest

# gcsfs est optionnel mais recommandé pour certains tests
pip install gcsfs

# Ou avec pipenv (recommandé)
pipenv install google-cloud-storage pytest gcsfs
```

## 🚀 Utilisation rapide

### 1. Démarrage en une ligne

```bash
# Installation et démarrage automatique
./scripts/fake-gcs-binary-manager.sh install
./scripts/fake-gcs-binary-manager.sh start

# Configuration de l'environnement pour les tests
export STORAGE_EMULATOR_HOST=localhost:4443
export GOOGLE_CLOUD_PROJECT=test-project
```

### 2. Commandes du gestionnaire de binaire

```bash
# Gestion du serveur
./scripts/fake-gcs-binary-manager.sh install    # Installe le binaire
./scripts/fake-gcs-binary-manager.sh start      # Démarre le serveur
./scripts/fake-gcs-binary-manager.sh stop       # Arrête le serveur
./scripts/fake-gcs-binary-manager.sh status     # Vérifie le statut
./scripts/fake-gcs-binary-manager.sh logs       # Affiche les logs
./scripts/fake-gcs-binary-manager.sh test       # Configure l'environnement de test

# Maintenance
./scripts/fake-gcs-binary-manager.sh clean      # Nettoie les données
./scripts/fake-gcs-binary-manager.sh uninstall  # Désinstalle tout
```

### 3. Tests avec pytest

```bash
# Tests avec le binaire (recommandé)
pytest tests/integration/test_gcs_loader_binary.py -v

# Tests complets d'intégration
pytest tests/integration/test_gcs_loader_binary.py::TestCompleteWorkflow -v

# Tests de performance
pytest tests/integration/test_gcs_loader_binary.py::TestGcsLoaderWithBinary::test_performance_with_binary -v
```

## 🧪 Tests d'intégration

### 1. Structure des tests simplifiée

```
tests/
├── fixtures/
│   └── fake_gcs_binary_fixtures.py       # Fixtures pour binaire
├── integration/
│   ├── test_gcs_loader_binary.py          # Tests avec binaire (RECOMMANDÉ)
│   └── test_gcs_loader_with_fake_gcs.py   # Tests avec Docker (fallback)
└── utils/
    └── confluence_export_simulator.py     # Simulation d'exports Confluence
```

### 2. Exemple d'usage simple

```python
# Test simple avec le binaire
def test_confluence_loading():
    from tests.fixtures.fake_gcs_binary_fixtures import FakeGcsBinaryManager
    from kbotloadscheduler.loader.gcs.gcs_loader import GcsLoader

    # Démarrage automatique du serveur
    manager = FakeGcsBinaryManager()
    manager.start()

    try:
        # Configuration
        os.environ["STORAGE_EMULATOR_HOST"] = "localhost:4443"
        os.environ["GOOGLE_CLOUD_PROJECT"] = "test-project"

        # Test du loader
        loader = GcsLoader()
        # ... vos tests ici ...

    finally:
        manager.stop()
```

### 3. Variables d'environnement

```bash
# Configuration pour les tests
export STORAGE_EMULATOR_HOST=localhost:4443
export GOOGLE_CLOUD_PROJECT=test-project
export USE_MOCKS=false  # Désactive les mocks internes

# Optionnel : skip des tests gcsfs si non installé
export SKIP_GCSFS_TESTS=true
```

## 🔧 Configuration avancée

### 1. Ports personnalisés

```bash
# Démarrage sur un port personnalisé
./scripts/fake-gcs-binary-manager.sh start 8080

# Avec HTTPS
./scripts/fake-gcs-binary-manager.sh start 8080 https
```

### 2. Backends de stockage

```bash
# Mémoire (défaut, recommandé pour tests)
./scripts/fake-gcs-binary-manager.sh start 4443 http memory

# Système de fichiers (persistant)
./scripts/fake-gcs-binary-manager.sh start 4443 http filesystem
```

## 🐛 Résolution de problèmes

### Problèmes courants

#### 1. Binaire non trouvé
```bash
# Solution: Installer le binaire
./scripts/fake-gcs-binary-manager.sh install

# Vérifier l'installation
./scripts/fake-gcs-binary-manager.sh status
```

#### 2. Port déjà utilisé
```bash
# Vérifier les ports utilisés
lsof -i :4443

# Utiliser un port différent
./scripts/fake-gcs-binary-manager.sh start 8080
export STORAGE_EMULATOR_HOST=localhost:8080
```

#### 3. Erreurs de permissions
```bash
# Rendre le script exécutable
chmod +x ./scripts/fake-gcs-binary-manager.sh

# Vérifier les permissions du binaire
ls -la ~/.local/bin/fake-gcs-server
```

#### 4. Tests qui échouent
```bash
# Vérifier que le serveur fonctionne
curl http://localhost:4443/storage/v1/b

# Vérifier les variables d'environnement
echo $STORAGE_EMULATOR_HOST
echo $GOOGLE_CLOUD_PROJECT

# Relancer avec des logs détaillés
./scripts/fake-gcs-binary-manager.sh logs
```

### Logs de débogage

```bash
# Logs en temps réel
./scripts/fake-gcs-binary-manager.sh logs

# Fichier de log direct
tail -f /tmp/fake-gcs-server.log

# Statut détaillé
./scripts/fake-gcs-binary-manager.sh status
```

## 📊 Comparaison : Binaire vs Docker

| Aspect | Binaire | Docker |
|--------|---------|--------|
| **Installation** | 1 commande | Plusieurs étapes |
| **Taille** | ~20MB | ~100MB+ |
| **RAM** | ~10MB | ~50MB+ |
| **Démarrage** | <1s | 5-10s |
| **Multiplateforme** | Natif | Émulation possible |
| **Dépendances** | Aucune | Docker requis |
| **Portabilité** | Très haute | Moyenne |

## � Migration depuis Docker

Si vous utilisez déjà la solution Docker :

```bash
# 1. Arrêter Docker
docker-compose -f docker-compose.test.yml down

# 2. Installer le binaire
./scripts/fake-gcs-binary-manager.sh install

# 3. Démarrer le binaire
./scripts/fake-gcs-binary-manager.sh start

# 4. Utiliser les nouveaux tests
pytest tests/integration/test_gcs_loader_binary.py -v
```

## 🎯 Recommandations

### Pour les tests de développement
```bash
# Solution simple et rapide
./scripts/fake-gcs-binary-manager.sh test
pytest tests/integration/test_gcs_loader_binary.py -v
```

### Pour l'intégration CI/CD
```bash
# GitHub Actions example
- name: Setup fake-gcs-server
  run: |
    ./scripts/fake-gcs-binary-manager.sh install
    ./scripts/fake-gcs-binary-manager.sh start

- name: Run tests
  run: |
    export STORAGE_EMULATOR_HOST=localhost:4443
    export GOOGLE_CLOUD_PROJECT=test-project
    pytest tests/integration/test_gcs_loader_binary.py -v
```

Cette solution binaire est **recommandée** car elle est plus simple, plus rapide et fonctionne nativement sur toutes les plateformes sans Docker !
