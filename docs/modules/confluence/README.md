# Documentation Complète du Module Confluence

Ce module est conçu pour découvrir, filtrer et télécharger de manière fiable et performante le contenu d'une ou plusieurs instances Confluence.

Ce document sert de portail central pour toute la documentation.

## Table des Matières

### 1. Vue d'Ensemble & Architecture
*   **[Aperçu de l'Architecture](ARCHITECTURE_DIAGRAMS.md)**
    > *Diagrammes visuels des composants clés et des flux de données.*
*   **[Diagrammes d'Architecture Détaillés](ARCHITECTURE_DIAGRAMS.md)**
    > *Diagrammes Mermaid illustrant l'architecture de haut niveau, les flux de données et les processus.*

### 2. Guides d'Utilisation Pratique
*   **[Guide de Configuration Complet](CONFIGURATION_GUIDE.md)**
    > *Référence exhaustive de toutes les options de configuration JSON, avec exemples et valeurs par défaut.*
*   **[Exemples d'Utilisation de l'API](API_USAGE_EXAMPLES_GUIDE.md)**
    > *Exemples de code Python pour les workflows courants (découverte, téléchargement, etc.).*
*   **[Sécurité et Authentification](SECURITY_AND_AUTHENTICATION_GUIDE.md)**
    > *Comment configurer les jetons API/PAT, gérer les secrets et les bonnes pratiques de sécurité.*
*   **[Guide de Dépannage (Troubleshooting)](TROUBLESHOOTING_GUIDE.md)**
    > *Solutions aux problèmes courants (authentification, réseau, configuration).*

### 3. Référence pour les Développeurs
*   **[Référence de l'API Programmatique](API_REFERENCE.md)**
    > *Documentation détaillée des classes, méthodes et exceptions du module.*
*   **[Guide des Tests et du Mocking](TESTING_AND_MOCKING_GUIDE.md)**
    > *Comment utiliser le mode "mock" pour le développement et les tests unitaires.*

### 4. Guides Spécifiques
*   **[Traitement des Diagrammes Draw.io](DRAWIO_PROCESSING_GUIDE.md)**
    > *Explication du pipeline de détection, d'extraction et de conversion des diagrammes Draw.io en Markdown.*
*   **[Guide de Traitement et de Nommage des Fichiers](FILE_PROCESSING_AND_NAMING_GUIDE.md)**
    > *Détails sur la manière dont les fichiers sont nommés, sauvegardés et comment les doublons sont gérés.*