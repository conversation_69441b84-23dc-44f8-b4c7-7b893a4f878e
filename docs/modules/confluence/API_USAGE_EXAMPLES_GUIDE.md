# Guide des Exemples d'Utilisation de l'API

## Aperçu

Ce guide fournit des exemples concrets d'utilisation de l'API du module Confluence. Il couvre les workflows de découverte, de téléchargement et de traitement des données.

## Workflows de Base

### Découverte des Documents

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source
source = SourceBean(
    src_type="",
    configuration='{"spaces":["TECH"],"export_format":"markdown"}'
)

# Récupérer la liste des documents
documents = loader.get_document_list(source)

# Afficher les résultats
for doc in documents:
    print(f"ID: {doc.id}, Nom: {doc.name}, Chemin: {doc.path}")
```

#### Explication

1. **Initialisation du Chargeur** : Crée une instance de `ConfluenceLoader` avec la configuration et les secrets nécessaires.
2. **Définition de la Source** : Crée un objet `SourceBean` qui spécifie le type de source (Confluence) et les paramètres de configuration.
3. **Récupération de la Liste des Documents** : Appelle la méthode `get_document_list` pour récupérer une liste d'objets `DocumentBean`.
4. **Affichage des Résultats** : Parcourt la liste des documents et affiche les informations pertinentes.

### Téléchargement d'un Document Spécifique

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces":["TECH"],"export_format":"markdown"}'
)

# Définir le document à télécharger
document = DocumentBean(
    id="TECH|confluence_docs|123456",
    path="https://your-confluence.com/display/TECH/Nom+du+Document",
    name="Nom du Document"
)

# Définir le chemin de sortie
output_path = "/chemin/de/sortie"

# Télécharger le document
metadata = loader.get_document(source, document, output_path)

# Afficher les métadonnées
print(f"Chemin du fichier: {metadata['location']}")
print(f"Taille du fichier: {metadata['file_size']}")
```

#### Explication

1. **Initialisation du Chargeur** : Crée une instance de `ConfluenceLoader` avec la configuration et les secrets nécessaires.
2. **Définition de la Source** : Crée un objet `SourceBean` qui spécifie le type de source (Confluence) et les paramètres de configuration.
3. **Définition du Document** : Crée un objet `DocumentBean` qui spécifie l'ID, le chemin et le nom du document à télécharger.
4. **Définition du Chemin de Sortie** : Spécifie le chemin où le document sera enregistré.
5. **Téléchargement du Document** : Appelle la méthode `get_document` pour télécharger le document et récupérer les métadonnées.
6. **Affichage des Métadonnées** : Affiche les métadonnées du document téléchargé, telles que le chemin du fichier et la taille du fichier.

## Workflows Avancés

### Traitement des Pièces Jointes

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces":["TECH"],"include_attachments":true,"file_extensions":["pdf", "docx"]}'
)

# Récupérer la liste des documents
documents = loader.get_document_list(source)

# Parcourir les documents
for doc in documents:
    if "att" in doc.id:
        # Télécharger la pièce jointe
        output_path = "/chemin/de/sortie"
        metadata = loader.get_document(source, doc, output_path)
        print(f"Pièce jointe téléchargée : {metadata['location']}")
```

#### Explication

1. **Configuration de la Source** : Configure la source pour inclure les pièces jointes et spécifie les extensions de fichiers à inclure.
2. **Récupération de la Liste des Documents** : Appelle la méthode `get_document_list` pour récupérer une liste d'objets `DocumentBean`, y compris les pièces jointes.
3. **Parcours des Documents** : Parcourt la liste des documents et vérifie si l'ID du document contient "att", ce qui indique qu'il s'agit d'une pièce jointe.
4. **Téléchargement de la Pièce Jointe** : Appelle la méthode `get_document` pour télécharger la pièce jointe et affiche le chemin du fichier téléchargé.

### Traitement des Diagrammes Draw.io

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces":["TECH"],"include_attachments":true,"extract_drawio_as_documents":true}'
)

# Récupérer la liste des documents
documents = loader.get_document_list(source)

# Parcourir les documents
for doc in documents:
    if "drawio" in doc.id:
        # Télécharger le diagramme Draw.io
        output_path = "/chemin/de/sortie"
        metadata = loader.get_document(source, doc, output_path)
        print(f"Diagramme Draw.io téléchargé : {metadata['location']}")
```

#### Explication

1. **Configuration de la Source** : Configure la source pour inclure les pièces jointes et extraire les diagrammes Draw.io en tant que documents distincts.
2. **Récupération de la Liste des Documents** : Appelle la méthode `get_document_list` pour récupérer une liste d'objets `DocumentBean`, y compris les diagrammes Draw.io.
3. **Parcours des Documents** : Parcourt la liste des documents et vérifie si l'ID du document contient "drawio", ce qui indique qu'il s'agit d'un diagramme Draw.io.
4. **Téléchargement du Diagramme Draw.io** : Appelle la méthode `get_document` pour télécharger le diagramme Draw.io et affiche le chemin du fichier téléchargé.

## Configuration Avancée

### Utilisation de CQL Personnalisé

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source avec CQL personnalisé
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces":["TECH"],"filtering":{"custom_cql":"space = \'TECH\' AND label = \'important\'"}}'
)

# Récupérer la liste des documents
documents = loader.get_document_list(source)

# Afficher les résultats
for doc in documents:
    print(f"ID: {doc.id}, Nom: {doc.name}, Chemin: {doc.path}")
```

#### Explication

1. **Configuration de la Source avec CQL** : Configure la source pour utiliser une requête CQL personnalisée afin de filtrer les documents.
2. **Récupération de la Liste des Documents** : Appelle la méthode `get_document_list` pour récupérer une liste d'objets `DocumentBean` qui correspondent à la requête CQL.
3. **Affichage des Résultats** : Parcourt la liste des documents et affiche les informations pertinentes.

### Gestion des Erreurs

#### Exemple de Code

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.loader.confluence.exceptions import ConfluenceException

# Initialiser le chargeur
loader = ConfluenceLoader(config_with_secret)

# Définir la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces":["TECH"],"export_format":"markdown"}'
)

try:
    # Récupérer la liste des documents
    documents = loader.get_document_list(source)

    # Afficher les résultats
    for doc in documents:
        print(f"ID: {doc.id}, Nom: {doc.name}, Chemin: {doc.path}")

except ConfluenceException as e:
    print(f"Erreur Confluence : {e}")
```

#### Explication

1. **Gestion des Exceptions** : Utilise un bloc `try...except` pour capturer les exceptions spécifiques à Confluence.
2. **Capture de ConfluenceException** : Capture l'exception `ConfluenceException` ou ses sous-classes pour gérer les erreurs liées à Confluence.
3. **Affichage de l'Erreur** : Affiche un message d'erreur informatif en cas d'exception.

## Bonnes Pratiques

### Configuration de la Journalisation

#### Exemple de Code

```python
import logging

# Configurer la journalisation
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Utiliser la journalisation
logging.info("Début du processus de récupération des documents Confluence")
try:
    documents = loader.get_document_list(source)
    logging.info(f"Nombre de documents récupérés : {len(documents)}")
except ConfluenceException as e:
    logging.error(f"Erreur Confluence : {e}", exc_info=True)
```

#### Explication

1. **Configuration de la Journalisation** : Configure la journalisation pour enregistrer les événements importants.
2. **Utilisation de la Journalisation** : Utilise les méthodes `logging.info` et `logging.error` pour enregistrer les informations et les erreurs.

### Gestion des Secrets

#### Exemple de Code

```python
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

# Récupérer les secrets
config_with_secret = ConfigWithSecret()
confluence_credentials = config_with_secret.get_global_confluence_credentials()

# Utiliser les secrets
confluence_url = confluence_credentials['confluence_url']
username = confluence_credentials['username']
api_token = confluence_credentials['api_token']
```

#### Explication

1. **Récupération des Secrets** : Utilise la classe `ConfigWithSecret` pour récupérer les secrets de manière sécurisée.
2. **Utilisation des Secrets** : Utilise les secrets récupérés pour configurer l'authentification avec Confluence.

### Gestion des Timeouts

#### Exemple de Code

```python
import requests

# Configurer les timeouts
try:
    response = requests.get(
        "https://your-confluence.com/rest/api/space",
        auth=('username', 'token'),
        timeout=10  # Timeout en secondes
    )
    response.raise_for_status()
except requests.exceptions.Timeout:
    print("Erreur : Délai d'attente dépassé")
except requests.exceptions.RequestException as e:
    print(f"Erreur : {e}")
```

#### Explication

1. **Configuration des Timeouts** : Utilise le paramètre `timeout` de la méthode `requests.get` pour configurer les délais d'attente.
2. **Gestion des Exceptions** : Capture les exceptions `requests.exceptions.Timeout` et `requests.exceptions.RequestException` pour gérer les erreurs de timeout et autres erreurs de requête.

Ce guide fournit des exemples concrets et des bonnes pratiques pour utiliser l'API du module Confluence de manière efficace et sécurisée.
