# Sécurité et Authentification - Guide Complet

## Aperçu

Ce guide couvre les pratiques de sécurité complètes pour le module Confluence, y compris les méthodes d'authentification, la gestion des identifiants et les meilleures pratiques de sécurité.

## Méthodes d'authentification

### Matrice des méthodes

| Méthode | Environnement | Priorité | Niveau de sécurité | Complexité de configuration |
|---|---|---|---|---|
| **Jeton d'accès personnel (PAT)** | Server/DC | Élevée | Excellent | Faible |
| **Jeton API** | Cloud | Élevée | Très bon | Moyenne |
| **OAuth 2.0** | Cloud | Non supporté | - | Non implémenté |
| **Nom d'utilisateur/Mot de passe**| Hérité (Legacy) | À éviter | Faible | Élevée |

## Jeton d'accès personnel (PAT) - Server/Data Center

### Étapes de configuration

#### 1. G<PERSON><PERSON><PERSON> le PAT dans Confluence
1. Allez dans **Paramètres du compte** → **Jetons d'accès personnels**
2. C<PERSON>z sur **Créer un PAT**
3. Saisissez le nom du jeton : `KBot-Reader-SVC`
4. Définissez l'expiration : **1 an** (recommandé)
5. Copiez le jeton généré de manière sécurisée

#### 2. Stocker dans un gestionnaire de secrets

**Azure Key Vault** :
```bash
# Créer le secret
az keyvault secret set \
  --vault-name kv-production-cc \
  --name "confluence-credentials" \
  --value '{
    "confluence_url": "https://confluence.company.com",
    "cloud": false,
    "pat_token": "CMc5L...",
    "username": null,
    "api_token": null
  }' --encoding base64

# Vérifier
az keyvault secret show --vault-name kv-production-cc --name "confluence-credentials"
```

**AWS Secrets Manager** :
```bash
aws secretsmanager create-secret \
  --name "confluence-credentials" \
  --secret-string '{
    "confluence_url": "https://confluence.company.com",
    "cloud": false,
    "pat_token": "CMc5L...",
    "username": null,
    "api_token": null
  }'
```

**HashiCorp Vault** :
```bash
vault kv put secret/confluence/credentials \
  confluence_url="https://confluence.company.com" \
  cloud="false" \
  pat_token="CMc5L..."
```

## Jeton API - Confluence Cloud

### Étapes de configuration

#### 1. Générer le jeton API
1. Allez dans **Paramètres du compte** → **Sécurité** → **Créer et gérer les jetons API**
2. Cliquez sur **Créer un jeton API**
3. Saisissez une étiquette : `KBot-Confluence-API`
4. Copiez le jeton (il commence par `ATATT...`)

#### 2. Stocker la configuration sécurisée
```json
{
  "confluence_url": "https://company.atlassian.net/wiki",
  "cloud": true,
  "username": "<EMAIL>",
  "api_token": "ATATTxxxxxxxxxxxxx",
  "pat_token": null
}
```

### Portées de jeton requises
- Accès en **lecture** aux espaces et aux pages
- Accès en **lecture** aux pièces jointes
- Accès en **lecture** aux profils utilisateur
- L'accès en **écriture** n'est PAS requis

## Meilleures pratiques de gestion des secrets

### 1. Secrets spécifiques à l'environnement

#### Développement
```bash
# Fichier .env.development (non commit)
CONFLUENCE_URL=https://dev-confluence.company.com
CONFLUENCE_CLOUD=false
CONFLUENCE_PAT_TOKEN=DEV_123456789
```

#### Staging (Pré-production)
```bash
az keyvault secret set \
  --vault-name kv-staging-cc \
  --name "staging-confluence-credentials" \
  --value '{"confluence_url":"https://stg-confluence.company.com","cloud":false,"pat_token":"STG_123456789"}'
```

#### Production
```bash
aws secretsmanager create-secret \
  --name "prod-confluence-credentials" \
  --secret-string '{"confluence_url":"https://confluence.company.com","cloud":false,"pat_token":"PROD_123456789"}'
```

### 2. Authentification basée sur le périmètre

#### Avantages
- Identifiants isolés par domaine/projet
- Contrôle d'accès granulaire
- Calendriers de rotation indépendants
- Piste d'audit par périmètre

#### Exemple de configuration
```json
{
  "auth": {
    "confluence_auth_mode": "perimeter"
  }
}
```

#### Convention de nommage des secrets
```
{perimeter-code}-confluence-credentials
```

Exemples :
- `product-tech-confluence-credentials`
- `marketing-kb-confluence-credentials`
- `finance-docs-confluence-credentials`

## Surveillance de la sécurité

### Vérifications de l'état des identifiants

#### Validation automatisée
```python
def validate_confluence_credentials(credentials: dict) -> bool:
    """Valide les identifiants et teste la connectivité."""
    try:
        client = ConfluenceClient(credentials)
        return client.test_connection()
    except Exception as e:
        logger.error(f"La validation des identifiants a échoué : {e}")
        return False
```

#### Script de vérification de l'état
```bash
#!/bin/bash
# credential_health_check.sh

source .env

response=$(curl -s -w "%{http_code}" -u "${CONFLUENCE_USERNAME}:${CONFLUENCE_API_TOKEN}" \
  "${CONFLUENCE_URL}/rest/api/space?start=0&limit=1")

http_code=${response: -3}

if [ $http_code -eq 200 ]; then
    echo "✓ Identifiants valides"
    exit 0
else
    echo "✗ Échec des identifiants avec le code : $http_code"
    exit 1
fi
```

### Stratégie de rotation des jetons

#### Rotation mensuelle
```yaml
# Workflow GitHub Actions
name: Rotation des identifiants Confluence
on:
  schedule:
    - cron: '0 0 1 * *'  # Mensuel

jobs:
  rotate-token:
    runs-on: ubuntu-latest
    steps:
      - name: Générer un nouveau PAT
        run: |
          # Générer et stocker le nouveau jeton
          new_token=$(confluence-api-generate-pat)

      - name: Mettre à jour le secret
        run: |
          az keyvault secret set \
            --vault-name kv-production-cc \
            --name "confluence-credentials" \
            --value $(echo "$new_token" | base64)

      - name: Vérifier le nouveau jeton
        run: |
          python scripts/verify_credentials.py
```

## Sécurité réseau

### Configuration TLS/SSL

#### Validation des certificats
```python
# Configuration du client avec une AC personnalisée
import requests
from kbotloadscheduler.loader.confluence.client import ConfluenceClient

# Certificats personnalisés
client = ConfluenceClient(
    credentials=credentials,
    verify='/path/to/ca-bundle.pem',
    timeout=30
)
```

#### Épinglage SSL (SSL Pinning) pour les environnements à haute sécurité
```python
import ssl
import certifi
from atlassian import Confluence

def create_secure_confluence_client(credentials):
    """Crée un client Confluence avec épinglage SSL."""

    # Contexte SSL personnalisé
    ssl_context = ssl.create_default_context()
    ssl_context.load_verify_locations(certifi.where())

    return Confluence(
        url=credentials.url,
        username=credentials.username,
        password=credentials.api_token,
        cloud=credentials.cloud,
        verify_ssl=True,
        ssl_context=ssl_context
    )
```

### Mise sur liste blanche d'IP (IP Whitelisting)

#### Groupes de sécurité AWS (Exemple)
```yaml
# Configuration Terraform
resource "aws_security_group" "confluence_outbound" {
  name_prefix = "confluence-outbound"

  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["X.X.X.X/32"]  # IP de Confluence
  }

  tags = {
    Name = "confluence-outbound-security-group"
  }
}
```

### Règles de pare-feu

#### Configuration d'iptables
```bash
# Autoriser le trafic HTTPS sortant vers Confluence
sudo iptables -A OUTPUT -p tcp --dport 443 -d X.X.X.X -j ACCEPT

# Journaliser l'activité suspecte
sudo iptables -A OUTPUT -p tcp --dport 443 -j LOG --log-prefix "SORTIE Confluence : "
```

## Gestion des erreurs et événements de sécurité

### Surveillance des échecs d'authentification

#### Événements de sécurité à surveiller
| Type d'événement | Niveau d'alerte | Action requise |
|---|---|---|
| **Identifiants invalides** | Élevé | Rotation immédiate des jetons |
| **403 Interdit (Forbidden)** | Moyen | Vérifier les permissions |
| **Échecs 401 multiples** | Critique | Compromission possible du jeton |
| **Accès depuis une IP inhabituelle** | Élevé | Enquêter immédiatement |
| **Verrouillages de comptes multiples** | Critique | Revue de la politique |

#### Configuration des alertes de sécurité
```python
# Configuration de la surveillance de sécurité
security_alerts = {
    "invalid_credentials": {
        "threshold": 3,
        "window": "5m",
        "notification": "security-alerts-channel"
    },
    "forbidden_access": {
        "threshold": 10,
        "window": "10m",
        "notification": "it-alerts-channel"
    },
    "unusual_activity": {
        "threshold": 1,
        "window": "immediate"
    }
}
```

### Piste d'audit

#### Journalisation de l'accès aux identifiants
```python
import logging
import hashlib
from datetime import datetime

class AuditLogger:
    """Journalisation d'audit pour l'accès aux identifiants."""

    def log_credential_access(self, credential_hash: str, source_ip: str):
        """Journalise l'accès aux identifiants pour la piste d'audit."""

        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'credential_md5': hashlib.md5(credential_hash.encode()).hexdigest(),
            'source_ip': source_ip,
            'operation': 'confluence_credential_access',
            'user_agent': request.headers.get('User-Agent')
        }

        audit_logger.info(json.dumps(log_entry))
```

## Conformité et Gouvernance

### Conformité réglementaire

#### SOC 2 Type II
- **Contrôle d'accès** : Principe du moindre privilège
- **Chiffrement des données** : Tous les identifiants chiffrés au repos
- **Journaux d'audit** : Piste d'accès complète
- **Gestion des changements** : Rotation automatisée

#### Conformité RGPD
```json
{
  "compliance": {
    "data_processing": {
      "purpose": "Traitement du contenu pour la base de connaissances",
      "lawful_basis": "intérêt_légitime",
      "retention_period": "24_mois"
    },
    "data_transfer": {
      "adequacy_decision": true,
      "cross_border_transfer": false
    }
  }
}
```

### Processus de revue des accès

#### Revues d'accès trimestrielles
```sql
-- Générer le rapport d'accès
SELECT
    credential_hash,
    last_access_time,
    access_count_90d,
    last_rotation_date
FROM credential_audit
WHERE
    last_access_time < NOW() - INTERVAL '90 days'
    OR last_rotation_date < NOW() - INTERVAL '90 days';
```

#### Revue automatisée
```python
def quarterly_access_review():
    """Génère le rapport de revue d'accès trimestriel."""

    review_data = {
        "credential_count": len(active_credentials),
        "expired_credentials": credentials_needing_rotation(),
        "inactive_credentials": credentials_inactive_90d(),
        "high_frequency_access": high_frequency_access_checks(),
        "recommendations": generate_recommendations()
    }

    return review_data
```

## Exemples d'intégration

### Intégration avec les rôles IAM d'AWS
```python
import boto3
from botocore.config import Config

def get_confluence_secret_with_iam():
    """Récupère les identifiants Confluence en utilisant un rôle IAM."""

    session = boto3.Session()

    # Utiliser le rôle IAM pour l'accès à AWS
    secrets_client = session.client('secretsmanager',
        config=Config(signature_version='v4'))

    response = secrets_client.get_secret_value(
        SecretId='prod-confluence-credentials'
    )

    return json.loads(response['SecretString'])
```

### Gestion des secrets Kubernetes
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: confluence-credentials
  namespace: kbot-processing
type: Opaque
stringData:
  confluence_url: "https://confluence.company.com"
  cloud: "false"
  pat_token: "PROD_TOKEN_HERE"
  username: ""
  api_token: ""
```

### Identifiants dynamiques avec HashiCorp Vault
```python
import hvac

def get_dynamic_confluence_credentials():
    """Obtient des identifiants à courte durée de vie depuis HashiCorp Vault."""

    client = hvac.Client(url='https://vault.company.com')

    # S'authentifier auprès de Vault
    client.auth.approle.login(role_id='<role-id>', secret_id='<secret-id>')

    # Demander des identifiants dynamiques
    response = client.secrets.database.generate_credentials(name='confluence-ro')

    return response['data']
```

## Dépannage de la sécurité

### Problèmes de sécurité courants

#### Problème : Fuite d'identifiants dans les journaux (logs)
**Symptômes** : Des jetons API apparaissent dans les journaux de l'application
**Solution** :
```python
import logging
import re

# Masquer les informations sensibles
def redact_sensitive_data(data: str) -> str:
    """Masque les jetons API et les jetons PAT des journaux."""

    # Modèles (patterns) de jetons
    patterns = [
        r'ATATT[0-9a-f-]+',      # Jeton API Atlassian
        r'CMc5L[A-Za-z0-9+/=]+',  # Modèle de jeton PAT
    ]

    redacted = data
```
