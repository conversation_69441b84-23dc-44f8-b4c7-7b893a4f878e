# Module Confluence - Guide de Dépannage Complet

## Commandes de Diagnostic Rapide

### 1. Test de Connectivité Réseau
```bash
# Connectivité de base
curl -m 30 -s -o /dev/null -w "%{http_code}" https://votre-confluence.com

# Test d'authentification
curl -u nom_utilisateur:jeton -s -o /dev/null -w "%{http_code}" \
  "https://votre-confluence.com/rest/api/space?start=0&limit=1"

# Validation du certificat SSL
openssl s_client -connect votre-confluence.com:443 -servername votre-confluence.com < /dev/null

# Vérification de la résolution DNS
nslookup votre-confluence.com
```

### 2. Bilan de Santé de Base
```bash
#!/bin/bash
# bilan_sante_rapide.sh

echo "=== Bilan de Santé du Module Confluence ==="
echo "1. Test de l'accès aux identifiants..."
curl -s -u "${CONFLUENCE_USERNAME}:${CONFLUENCE_API_TOKEN}" \
  "${CONFLUENCE_URL}/rest/api/space?start=0&limit=5" | jq '.results[] | {.key, .name}'

echo "2. Test de la fonctionnalité de recherche..."
curl -s -u "${CONFLUENCE_USERNAME}:${CONFLUENCE_API_TOKEN}" \
  "${CONFLUENCE_URL}/rest/api/content/search?cql=space%3DTECH&limit=1" | jq '.results[] | {.id, .title}'
```

## Problèmes Courants et Solutions

### 1. Échecs d'Authentification

#### Problème : 401 Non autorisé (Unauthorized)
**Symptômes** :
```
ConfluenceAuthenticationError: 401 Unauthorized
Échec de la connexion à l'API Confluence
```

**Étapes de Diagnostic** :
```python
# Tester les identifiants directement
def tester_identifiants():
    import requests
    from requests.auth import HTTPBasicAuth

    response = requests.get(
        f"{CONFLUENCE_URL}/rest/api/space",
        auth=HTTPBasicAuth(CONFLUENCE_USERNAME, CONFLUENCE_API_TOKEN),
        timeout=10
    )

    print(f"Statut : {response.status_code}")
    print(f"Réponse : {response.text[:200]}...")
    return response.status_code == 200
```

**Solutions** :
1.  **Vérifier la validité du jeton d'API** :
    ```bash
    # Vérifier que le jeton n'a pas expiré
    curl -u nom_utilisateur:jeton -I "https://votre-confluence.com/rest/api/user/current"
    ```

2.  **Vérifier le format de l'URL** :
    ```sql
    -- Formats valides
    https://votre-entreprise.atlassian.net/wiki     # Cloud
    https://confluence.votre-entreprise.com         # Server

    -- Formats invalides (erreurs courantes)
    https://confluence.votre-entreprise.com/wiki    # Double /wiki
    https://confluence.votre-entreprise.com:8443    # Mauvais port
    ```

3.  **Vérifier les permissions de l'utilisateur** :
    ```bash
    # Tester l'accès à l'espace
    curl -u nom_utilisateur:jeton \
      "https://votre-confluence.com/rest/api/space/TECH"
    ```

4.  **Vérification de la portée (scope) du jeton** :
    ```bash
    # Vérifier les capacités du jeton
    curl -H "Authorization: Basic $(echo -n nom_utilisateur:jeton | base64)" \
      "https://votre-confluence.com/rest/api/content?start=0&limit=1"
    ```

#### Problème : 403 Interdit (Forbidden)
**Symptômes** :
```
ConfluencePermissionError: 403 Forbidden
Permissions insuffisantes
```

**Solutions** :
1.  **Permissions au niveau de l'espace** :
    ```bash
    # Vérifier les permissions de l'espace
    curl -u nom_utilisateur:jeton \
      "https://votre-confluence.com/rest/api/space/VOTRE_ESPACE/permission"
    ```

2.  **Restrictions au niveau de la page** :
    ```python
    # Identifier les pages restreintes
    def trouver_contenu_restreint(space_key):
        # Utiliser CQL pour trouver les pages avec des restrictions
        cql = f'space = "{space_key}" AND restricted = "*"'
        response = client.search_content(cql)
        return response['results']
    ```

### 2. Problèmes de Réseau et de Performance

#### Problème : Délais d'attente (Timeouts) et Problèmes de Connexion
**Symptômes** :
```
ConfluenceTimeoutError: Connection timeout
Exception HTTPSConnectionPool
```

**Diagnostic** :
```python
def diagnostic_reseau():
    import requests
    import time

    # Tester la connectivité de base
    try:
        start = time.time()
        response = requests.get(
            CONFLUENCE_URL,
            timeout=5,
            headers={'Connection': 'close'}
        )
        dns_time = time.time() - start
        print(f"✓ Connectivité de base : {dns_time:.2f}s")
    except Exception as e:
        print(f"✗ Problème réseau : {e}")

    # Tester la résolution DNS
    try:
        import socket
        ip = socket.gethostbyname(CONFLUENCE_URL.replace('https://', ''))
        print(f"✓ DNS résolu : {ip}")
    except Exception as e:
        print(f"✗ Problème DNS : {e}")
```

**Solutions** :
1.  **Ajuster les délais d'attente (timeouts)** :
    ```json
    {
      "performance": {
        "retry_attempts": 3,
        "retry_delay_seconds": 5,
        "circuit_breaker_timeout_seconds": 300
      }
    }
    ```

2.  **Configuration du proxy** :
    ```python
    import os
    os.environ['HTTPS_PROXY'] = 'http://nom_utilisateur:<EMAIL>:8080'
    os.environ['HTTPS_PROXY'] = 'http://proxy.entreprise.com:8080'
    ```

#### Problème : Limitation de Débit (429)
**Symptômes** :
```
ConfluenceRateLimitError: 429 Too Many Requests
```

**Solutions** :
```json
{
  "performance": {
    "max_parallel_workers": 1,
    "retry_attempts": 5,
    "retry_delay_seconds": 10,
    "circuit_breaker_threshold": 3,
    "circuit_breaker_timeout_seconds": 180
  }
}
```

### 3. Problèmes de Configuration

#### Problème : Configuration Invalide
**Symptômes** :
```
ConfluenceConfigurationError: Invalid configuration
```

**Script de Validation** :
```python
#!/usr/bin/env python3
# valider_config.py

import json
from kbotloadscheduler.loader.confluence.config import build_config_from_source
from kbotloadscheduler.bean.beans import SourceBean

def valider_config(config_json):
    """Valider la configuration pour les problèmes courants."""

    try:
        config_str = json.dumps(config_json)
        source = SourceBean(configuration=config_str)
        config = build_config_from_source(source)

        print("✓ La configuration est valide")

        # Validation de base
        spaces = config.basic.spaces
        if not spaces:
            print("⚠ Aucun espace configuré")

        if config.basic.max_results is None:
            print("⚠ Aucune limite de résultats définie - peut consommer beaucoup de mémoire")

        return True

    except Exception as e:
        print(f"✗ Erreur de configuration : {e}")
        return False

# Exemples d'utilisation
if __name__ == "__main__":
    # Configurations de test
    configs = [
        {"basic": {"spaces": ["TECH"], "export_format": "markdown"}},
        {"invalid": {"spaces": "not_an_array"}}  # Ceci échouera
    ]

    for config in configs:
        valider_config(config)
```

#### Problème : Validation de la Clé d'Espace
**Symptômes** :
```
Espace non trouvé pour la clé : ESPACE_INVALIDE
```

**Outil de Découverte d'Espaces** :
```python
#!/usr/bin/env python3
# decouvrir_espaces.py

import requests
from requests.auth import HTTPBasicAuth
import sys

def decouvrir_espaces(confluence_url, username, password):
    """Lister tous les espaces accessibles."""

    auth = HTTPBasicAuth(username, password)

    # Obtenir tous les espaces
    response = requests.get(
        f"{confluence_url}/rest/api/space",
        auth=auth,
        params={'limit': 50}
    )

    if response.status_code != 200:
        print(f"Erreur : {response.status_code}")
        return []

    spaces = response.json()
    return spaces['results']

# Utilisation
if __name__ == "__main__":
    print("Espaces disponibles :")
    spaces = decouvrir_espaces(CONFLUENCE_URL, USERNAME, TOKEN)
    for space in spaces:
        print(f"  {space['key']} - {space['name']}")
```

### 4. Problèmes de Performance

#### Problème : Épuisement de la Mémoire
**Symptômes** :
```
Erreur de mémoire insuffisante (Out of memory)
Le traitement se bloque sur les grands espaces
```

**Solutions** :
1.  **Activer le traitement à faible consommation de mémoire** :
    ```json
    {
      "performance": {
        "use_memory_efficient_processing": true,
        "max_parallel_workers": 1,
        "cache_ttl_minutes": 30
      },
      "basic": {
        "max_results": 1000,
        "child_page_depth": 5
      }
    }
    ```

2.  **Traiter les espaces individuellement** :
    ```python
    def traiter_espaces_sequentiellement(space_keys):
        """Traiter les espaces un par un pour gérer la mémoire."""
        for space_key in space_keys:
            print(f"Traitement de l'espace : {space_key}")
            config = {
                "basic": {"spaces": [space_key], "max_results": 500},
                "performance": {"use_memory_efficient_processing": true}
            }
            process_space(space_key, config)
    ```

3.  **Traitement par lots (chunking)** :
    ```python
    def chunk_configuration(original_config, chunk_size=100):
        """Diviser les grandes configurations en lots gérables."""

        spaces = original_config['basic']['spaces']
        max_results = original_config['basic']['max_results']

        for i in range(0, len(spaces), chunk_size):
            chunk = spaces[i:i+chunk_size]

            chunk_config = original_config.copy()
            chunk_config['basic']['spaces'] = chunk
            chunk_config['basic']['max_results'] = min(chunk_size, max_results or 100)

            yield chunk_config
    ```

#### Problème : Traitement Lent
**Symptômes** :
- Le traitement prend des heures pour les grands espaces
- Avertissements de limitation de débit de l'API

**Outils de Diagnostic** :
```python
def diagnostic_performance(client, space_key):
    """Analyser les caractéristiques de performance d'un espace."""

    # Obtenir les statistiques de l'espace
    space_info = client.get_space_info(space_key)
    page_count = space_info['page_count']
    attachment_count = space_info['attachment_count']

    print(f"Espace : {space_key}")
    print(f"Nombre total de pages : {page_count}")
    print(f"Nombre total de pièces jointes : {attachment_count}")

    # Estimer le temps de traitement
    pages_per_minute = 60  # Estimation conservatrice

    estimated_time = page_count / pages_per_minute
    print(f"Temps de traitement estimé : {estimated_time:.1f} minutes")

    return {
        "space_key": space_key,
        "page_count": page_count,
        "attachment_count": attachment_count,
        "estimated_time_minutes": estimated_time
    }
```

### 5. Problèmes de Données

#### Problème : Pièces Jointes Manquantes
**Symptômes** :
- PDF attendus non trouvés
- Liens d'images brisés

**Diagnostic** :
```python
def verifier_pieces_jointes(space_key):
    """Vérifier la disponibilité des pièces jointes."""

    # Obtenir toutes les pages de l'espace
    pages = client.get_all_pages(space_key)

    missing_attachments = []
    for page in pages:
        attachments = client.get_page_attachments(page['id'])

        for att in attachments:
            # Vérifier si le fichier existe
            try:
                response = requests.head(att['download_link'])
                if response.status_code != 200:
                    missing_attachments.append({
                        'page_id': page['id'],
                        'page_title': page['title'],
                        'attachment': att['filename'],
                        'status_code': response.status_code
                    })
            except Exception as e:
                missing_attachments.append({
                    'page_id': page['id'],
                    'page_title': page['title'],
                    'attachment': att['filename'],
                    'error': str(e)
                })

    return missing_attachments
```

#### Problème : Problèmes d'Encodage des Caractères
**Symptômes** :
- Caractères illisibles dans le markdown
- Erreurs Unicode

**Solutions** :
```python
def gerer_problemes_encodage():
    """Configurer la gestion correcte de l'encodage."""

    import sys

    # Configuration de l'encodage Python
    reload(sys)
    sys.setdefaultencoding('utf-8')

    # Variables d'environnement
    os.environ['PYTHONIOENCODING'] = 'utf-8'
```

### 6. Problèmes de Traitement Draw.io

#### Problème : Échecs de Détection des Diagrammes
**Symptômes** :
- Les pièces jointes Draw.io ne sont pas traitées
- Diagrammes manquants dans le résultat final

**Outils de Débogage** :
```python
def deboguer_traitement_drawio():
    """Déboguer la détection des diagrammes Draw.io."""

    from kbotloadscheduler.loader.confluence.drawio.detector import DrawioDetector

    detector = DrawioDetector()

    # Tester la reconnaissance de motifs
    test_content = """
    <ac:structured-macro ac:name="drawio">
        <ac:parameter ac:name="ssiョードsrcr</ac:parameter>
        <ac:rich-text-body>...</ac:rich-text-body>
    </ac:structured-macro>
    """

    # Valider la détection
    diagrams = detector.find_from_page_content(test_content)

    for diagram in diagrams:
        print(f"Diagramme trouvé : {diagram.diagram_id}")
        print(f"Type : {diagram.type}")
        print(f"Emplacement : {diagram.location}")
```

#### Problème : Erreurs d'Analyse XML

**Symptômes** :
```
Erreur lors de l'analyse du XML Draw.io
XML structurellement invalide
```

**Solutions** :

1. **Nettoyer le XML** :
   ```python
   import xml.etree.ElementTree as ET

   def nettoyer_xml(xml_content):
       """Nettoyer le XML pour corriger les erreurs d'analyse."""
       try:
           ET.fromstring(xml_content)
           return xml_content
       except ET.ParseError as e:
           print(f"Erreur d'analyse XML : {e}")
           # Implémenter une logique de nettoyage plus robuste ici
           return None
   ```

2. **Valider le contenu Draw.io** :
   Assurez-vous que le contenu de Draw.io est valide et non corrompu. Re-créez le diagramme si nécessaire.

### 7. Problèmes de Limitation de Débit (Rate Limiting)

#### Problème : Dépassement de la Limite de Requêtes
**Symptômes** :
```
ConfluenceRateLimitError: 429 Too Many Requests
```

**Solutions** :

1. **Implémenter une logique de réessai avec délai exponentiel** :
   ```python
   import time
   import requests

   def faire_requete_avec_reessai(url, headers, max_retries=3, initial_delay=1):
       """Effectuer une requête avec une logique de réessai en cas de limitation de débit."""
       for attempt in range(max_retries):
           response = requests.get(url, headers=headers)
           if response.status_code == 429:
               delay = initial_delay * (2 ** attempt)
               print(f"Limitation de débit détectée. Réessai dans {delay} secondes...")
               time.sleep(delay)
           else:
               return response
       print("Nombre maximal de tentatives atteint.")
       return None
   ```

2. **Réduire le nombre de requêtes parallèles** :
   Diminuez `max_parallel_workers` dans la configuration.

### 8. Problèmes d'Intégrité des Données

#### Problème : Données Manquantes ou Corrompues
**Symptômes** :
- Des pages ou des pièces jointes sont manquantes dans les résultats exportés.
- Des erreurs de hachage ou de validation de données se produisent.

**Solutions** :

1. **Effectuer une validation des données de bout en bout** :
   ```python
   import hashlib

   def valider_integrite_donnees(data, expected_hash):
       """Valider l'intégrité des données en comparant les hachages."""
       data_hash = hashlib.sha256(data).hexdigest()
       if data_hash == expected_hash:
           print("✓ Intégrité des données validée")
           return True
       else:
           print("✗ Erreur : Les données sont corrompues")
           return False
   ```

2. **Mettre en œuvre des mécanismes de sauvegarde et de restauration** :
   Sauvegardez régulièrement les données et mettez en place des procédures de restauration en cas de corruption.

           return None
   ```

2. **Valider le contenu Draw.io** :
   Assurez-vous que le contenu de Draw.io est valide et non corrompu. Re-créez le diagramme si nécessaire.

### 7. Problèmes de Limitation de Débit (Rate Limiting)

#### Problème : Dépassement de la Limite de Requêtes
**Symptômes** :
```
ConfluenceRateLimitError: 429 Too Many Requests
```

**Solutions** :

1. **Implémenter une logique de réessai avec délai exponentiel** :
   ```python
   import time
   import requests

   def faire_requete_avec_reessai(url, headers, max_retries=3, initial_delay=1):
       """Effectuer une requête avec une logique de réessai en cas de limitation de débit."""
       for attempt in range(max_retries):
           response = requests.get(url, headers=headers)
           if response.status_code == 429:
               delay = initial_delay * (2 ** attempt)
               print(f"Limitation de débit détectée. Réessai dans {delay} secondes...")
               time.sleep(delay)
           else:
               return response
       print("Nombre maximal de tentatives atteint.")
       return None
   ```

2. **Réduire le nombre de requêtes parallèles** :
   Diminuez `max_parallel_workers` dans la configuration.

### 8. Problèmes d'Intégrité des Données

#### Problème : Données Manquantes ou Corrompues
**Symptômes** :
- Des pages ou des pièces jointes sont manquantes dans les résultats exportés.
- Des erreurs de hachage ou de validation de données se produisent.

**Solutions** :

1. **Effectuer une validation des données de bout en bout** :
   ```python
   import hashlib

   def valider_integrite_donnees(data, expected_hash):
       """Valider l'intégrité des données en comparant les hachages."""
       data_hash = hashlib.sha256(data).hexdigest()
       if data_hash == expected_hash:
           print("✓ Intégrité des données validée")
           return True
       else:
           print("✗ Erreur : Les données sont corrompues")
           return False
   ```

2. **Mettre en œuvre des mécanismes de sauvegarde et de restauration** :
   Sauvegardez régulièrement les données et mettez en place des procédures de restauration en cas de corruption.
**Symptômes** :
