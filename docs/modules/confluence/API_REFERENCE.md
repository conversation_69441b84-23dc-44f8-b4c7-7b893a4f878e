# Module Confluence - Référence API Complète

Ce document fournit la référence de l'API programmatique pour les développeurs travaillant avec le module Confluence.

## Table des matières

1. [Classes principales](#classes-principales)
2. [Modèles de configuration](#modèles-de-configuration)
3. [API clientes](#api-clientes)
4. [Interfaces de traitement](#interfaces-de-traitement)
5. [Classes utilitaires](#classes-utilitaires)
6. [Gestion des exceptions](#gestion-des-exceptions)

---

## Classes principales

### ConfluenceLoader - Point d'entrée

Situé dans [`confluence_loader.py`](src/kbotloadscheduler/loader/confluence/confluence_loader.py:35)

```python
class ConfluenceLoader(AbstractLoader):
    """Chargeur principal pour le contenu Confluence."""

    def __init__(self, config: ConfigWithSecret, client: Optional[ConfluenceClient] = None)

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]
    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]
```

#### Exemple d'utilisation

```python
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

loader = ConfluenceLoader(config_with_secret)
source = SourceBean(
    src_type="",
    configuration='{"spaces":["TECH"],"export_format":"markdown"}'
)
documents = loader.get_document_list(source)
```

---

### ConfluenceClient - Client API

Situé dans [`confluence_client.py`](src/kbotloadscheduler/loader/confluence/client/confluence_client.py:1)

```python
class ConfluenceClient:
    """Client API robuste pour Confluence."""

    def __init__(self, credentials: ConfluenceCredentials, config: ConfluenceConfig)

    # Méthodes de contenu
    def get_page_content(self, page_id: str, export_format: str = "markdown") -> str
    def get_page_metadata(self, page_id: str) -> Dict[str, Any]
    def get_page_attachments(self, page_id: str, filename: str = None) -> List[Dict]
    def get_attachment_content(self, attachment_id: str) -> bytes

    # Méthodes de recherche
    def search_content(self, cql: str, limit: int = 100, start: int = 0, expand: str = None) -> Dict[str, Any]
    def get_space_count(self, space_key: str) -> int

    # Méthodes utilitaires
    def get_base_url(self) -> str
    def test_connection(self) -> bool
```

---

### Modèles de configuration

#### ConfluenceConfig - Configuration principale

Situé dans [`config/schema.py`](src/kbotloadscheduler/loader/confluence/config/schema.py:27)

```python
class ConfluenceConfig(BaseModel):
    """Configuration complète pour le chargeur Confluence."""

    basic: BasicConfig
    filtering: FilteringConfig
    attachments: AttachmentConfig
    performance: PerformanceConfig
    auth: AuthConfig
    file_processing: FileProcessingConfig

class BasicConfig(BaseModel):
    spaces: List[str]
    max_results: Optional[int]
    export_format: ExportFormat
    include_content_in_metadata: bool
    include_child_pages: bool
    child_page_depth: int

class FilteringConfig(BaseModel):
    labels: Optional[List[str]]
    exclude_labels: Optional[List[str]]
    last_modified_days: Optional[int]
    custom_cql: Optional[str]
```

#### Options d'ExportFormat
- `"markdown"` - Conversion en markdown propre
- `"pdf"` - Téléchargement de document PDF
- `"html"` - Export de contenu HTML

#### Validation de la configuration
```python
from kbotloadscheduler.loader.confluence.config import build_config_from_source
from kbotloadscheduler.bean.beans import SourceBean

source = SourceBean(configuration='{"spaces":["TECH"]}')
config = build_config_from_source(source)
# Retourne un objet ConfluenceConfig validé
```

---

## API clientes

### Authentification avec ConfluenceCredentials

Situé dans [`client/confluence_credentials.py`](src/kbotloadscheduler/loader/confluence/client/confluence_credentials.py:14)

```python
@dataclass
class ConfluenceCredentials:
    url: str
    username: str | None = None
    api_token: str | None = None
    pat_token: str | None = None
    cloud: bool = True

    @classmethod
    def from_env(cls) -> "ConfluenceCredentials"
    @classmethod
    def from_secret_dict(cls, secret: dict) -> "ConfluenceCredentials"
```

#### Configuration par variables d'environnement
```bash
# Fichier .env
CONFLUENCE_URL=https://company.atlassian.net/wiki
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=ATATTxxxxxxxxxxxxxxx
CONFLUENCE_CLOUD=true
```

#### Configuration via le gestionnaire de secrets
```json
{
  "confluence_url": "https://instance.atlassian.net/wiki",
  "cloud": true,
  "username": "<EMAIL>",
  "api_token": "ATATTxxxxxxxxxxxxxxx"
}
```

---

### Traitement des espaces (Space)

#### Interface SpaceProcessor

Situé dans [`space/space_processor.py`](src/kbotloadscheduler/loader/confluence/space/space_processor.py:35)

```python
class SpaceProcessor:
    """Traite un espace Confluence entier."""

    def __init__(self, client: ConfluenceClient, search: CqlSearch, base_url: str)

    def process(self, space_key: str, source: SourceBean, config: ConfluenceConfig) -> List[DocumentBean]
```

#### Processus de découverte
```python
from kbotloadscheduler.loader.confluence.space import SpaceProcessor
from kbotloadscheduler.loader.confluence.search import CqlSearch

# Initialiser les composants
search = CqlSearch(client, circuit_breaker)
processor = SpaceProcessor(client, search, base_url)

# Traiter un espace
documents = processor.process("TECH", source, config)
```

---

### Requêtes de recherche (CQL)

Situé dans [`search/cql_search.py`](src/kbotloadscheduler/loader/confluence/search/cql_search.py:16)

#### Construction de requêtes intégrée

**Requête Espace + Date** :
```sql
space = "TECH" AND lastModified >= "2024-01-01" AND lastModified <= "2024-12-31"
```

**Filtrage par étiquette** :
```sql
space = "TECH" AND label in ("documentation", "public") AND label not in ("archive")
```

#### Utilisation de CQL personnalisé
```python
config = ConfluenceConfig(
    filtering=FilteringConfig(
        custom_cql='space = "TECH" AND creator = "john.doe" AND title ~ "API"'
    )
)
```

---

### Traitement de Draw.io

#### Pipeline de traitement principal

Situé dans [`drawio/`](src/kbotloadscheduler/loader/confluence/drawio/)

```python
# Détection
from kbotloadscheduler.loader.confluence.drawio.detector import DrawioDetector

detector = DrawioDetector()
diagrams = detector.find_drawio_diagrams(page_content)

# Extraction
from kbotloadscheduler.loader.confluence.drawio.extractor import DrawioExtractor

extractor = DrawioExtractor()
metadata = extractor.extract_diagram_info(xml_content)

# Traitement
from kbotloadscheduler.loader.confluence.drawio.processor import DrawioProcessor

processor = DrawioProcessor()
markdown = processor.convert_to_markdown(diagram_data)
```

#### Exemples de détection Draw.io

```python
# Détecter à partir des données de la page
diagrams = detector.find_from_page_data(page_data)

# Détecter à partir des métadonnées des pièces jointes
diagrams = detector.find_from_attachment_meta(attachment_list)

# Modèles de détection personnalisés
patterns = detector.get_drawio_patterns()
```

---

### Téléchargeur de contenu (ContentDownloader)

Situé dans [`processors/content_downloader.py`](src/kbotloadscheduler/loader/confluence/processors/content_downloader.py:40)

```python
class ContentDownloader:
    """Télécharge et traite le contenu depuis Confluence."""

    def __init__(self, client: ConfluenceClient)

    def download_page(self, page_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]
    def download_attachment(self, attachment_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]
    def download_drawio_diagram(self, diagram_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]
```

#### Structure des métadonnées retournées
```python
{
    "location": "/path/to/downloaded/file.md",
    "file_size": 4567,
    "original_filename": "page-title",
    "safe_filename": "page-title_123456.md",
    "export_format": "markdown",
    "modificationDate": "2024-01-15T14:30:00",
    "parent_page_id": "4444555666"
}
```

---

## Classes utilitaires

### Gestion des identifiants de document

Situé dans [`utils/document_id_utils.py`](src/kbotloadscheduler/loader/confluence/utils/document_id_utils.py:12)

```python
class DocumentIdFormatter:
    """Gère le formatage et l'analyse des identifiants de document."""

    @classmethod
    def create_page_id(cls, source: SourceBean, page_id: str) -> str
    @classmethod
    def create_attachment_id(cls, source: SourceBean, attachment_id: str) -> str
    @classmethod
    def create_drawio_id(cls, source: SourceBean, diagram_id: str) -> str

    @classmethod
    def parse_document_id(cls, document_id: str) -> Dict[str, str]
    @classmethod
    def is_attachment_id(cls, confluence_id: str) -> bool
    @classmethod
    def is_drawio_diagram_id(cls, confluence_id: str) -> bool
```

#### Exemples d'utilisation
```python
# Créer des identifiants
source = SourceBean(code="my_source", domain_code="my_domain")
page_id = DocumentIdFormatter.create_page_id(source, "123456")
# Retourne : "my_domain|my_source|123456"

attachment_id = DocumentIdFormatter.create_attachment_id(source, "789012")
# Retourne : "my_domain|my_source|att789012"

# Analyser un identifiant existant
parsed = DocumentIdFormatter.parse_document_id("my_domain|my_source|att789012")
# Retourne : {"domain_code": "my_domain", "source_code": "my_source", "confluence_id": "att789012"}
```

### Utilitaires de fichiers

Situé dans [`utils/file_utils.py`](src/kbotloadscheduler/loader/confluence/utils/file_utils.py:1)

```python
# Génération de noms de fichiers sécurisés
from kbotloadscheduler.loader.confluence.utils.file_utils import create_safe_filename, create_unique_filename

safe_name = create_safe_filename("User Guide v2.0.pdf")
# Retourne : "user_guide_v2.0.pdf"

unique_name = create_unique_filename("document.pdf", "/output/dir")
# Retourne : "document_1.pdf" si une collision existe
```

### Utilitaires d'URL

Situé dans [`utils/url_utils.py`](src/kbotloadscheduler/loader/confluence/utils/url_utils.py:1)

```python
from kbotloadscheduler.loader.confluence.utils.url_utils import (
    extract_page_id_from_attachment_path,
    extract_page_id_from_url,
    extract_parent_page_url
)

page_id = extract_page_id_from_url("https://instance.atlassian.net/wiki/spaces/TECH/pages/123456")
# Retourne : "123456"
```

### Fabrique de tentatives (Retry Factory)

Situé dans [`utils/retry_factory.py`](src/kbotloadscheduler/loader/confluence/utils/retry_factory.py:1)

```python
from kbotloadscheduler.loader.confluence.utils.retry_factory import (
    create_api_retryer,
    create_search_retryer
)

# Obtenir un "retryer" pour des opérations spécifiques
api_retryer = create_api_retryer(config)
search_retryer = create_search_retryer(config)
```

---

## Gestion des exceptions

### Hiérarchie des exceptions

```python
ConfluenceException
├── ConfluenceConfigurationError    # Erreurs de validation
├── ConfluenceClientException       # Erreurs du client API
    ├── ConfluenceAuthenticationError   # 401 Unauthorized
    ├── ConfluencePermissionError       # 403 Forbidden
    ├── ConfluenceNotFoundError         # 404 Not found
    ├── ConfluenceRateLimitError        # 429 Too many requests
    ├── ConfluenceTimeoutError          # 408/504 timeouts
```

### Exemples de gestion des exceptions

```python
from kbotloadscheduler.loader.confluence.exceptions import (
    ConfluenceAuthenticationError,
    ConfluenceClientException,
    ConfluenceConfigurationError
)

try:
    documents = loader.get_document_list(source)
except ConfluenceAuthenticationError as e:
    logger.error(f"L'authentification a échoué : {e.message}")
    # Le jeton est peut-être invalide ou a expiré
    raise e
except ConfluenceConfigurationError as e:
    logger.error(f"Erreur de configuration dans {e.config_key}: {e.message}")
    # Corriger la configuration
    raise e
except ConfluenceClientException as e:
    logger.warning(f"Erreur API {e.status_code}: {e.message}")
    # Peut être une erreur transitoire
    raise e
```

---

## Stratégies de test

### Mise en place des tests mocks (simulés)

#### Configuration du mode mock
```python
import os
os.environ["MOCKING_ENABLED"] = "true"

# Toutes les opérations Confluence utiliseront des données simulées (mock data)
loader = ConfluenceLoader(config)
```

#### Configuration des données de test
Les données simulées sont automatiquement chargées depuis :
```
src/kbotloadscheduler/mock/confluence/mock_data/
├── cql_search_space_*.json
├── attachments_for_page_*.json
└── page_content_*.json
```

#### Modèle de test unitaire
```python
import pytest
from kbotloadscheduler.mock.confluence import create_mock_loader

@pytest.fixture
def mock_loader():
    return create_mock_loader()

def test_page_discovery(mock_loader):
    config = mock_confluence_config()
    docs = mock_loader.get_document_list(create_test_source())

    assert isinstance(docs, list)
    assert len(docs) > 0
    assert all(isinstance(doc, DocumentBean) for doc in docs)
```

---

Cette référence API couvre toutes les interfaces publiques et les composants internes clés pour une utilisation programmatique du module Confluence.
