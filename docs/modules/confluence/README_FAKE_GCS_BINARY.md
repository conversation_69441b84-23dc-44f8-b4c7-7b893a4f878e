# Tests GCS avec fake-gcs-server (Binaire)

## 🚀 Solution simple et rapide

Cette solution utilise le **binaire directement** au lieu de Docker, ce qui est plus simple, plus rapide et fonctionne nativement sur toutes les plateformes.

## ⚡ Démarrage ultra-rapide

### 1. Installation (une seule fois)
```bash
./scripts/fake-gcs-binary-manager.sh install
```

### 2. Démarrage du serveur
```bash
./scripts/fake-gcs-binary-manager.sh start
```

### 3. Test rapide
```bash
python ./scripts/quick-test-binary.py
```

### 4. Tests complets
```bash
export STORAGE_EMULATOR_HOST=localhost:4443
export GOOGLE_CLOUD_PROJECT=test-project
pytest tests/integration/test_gcs_loader_binary.py -v
```

## 🎯 Avantages de cette approche

| Aspect | Binaire | Docker |
|--------|---------|--------|
| Installation | 1 commande | Plusieurs étapes |
| Taille | ~20MB | ~100MB+ |
| RAM | ~10MB | ~50MB+ |
| Démarrage | <1s | 5-10s |
| Dépendances | Aucune | Docker requis |

## 🖥️ Compatibilité totale

- ✅ **macOS Intel** (x86_64)
- ✅ **macOS Apple Silicon** (M1/M2 ARM64)
- ✅ **Linux x86_64**
- ✅ **Linux ARM64**

## 📋 Commandes essentielles

```bash
# Gestion du serveur
./scripts/fake-gcs-binary-manager.sh start     # Démarre
./scripts/fake-gcs-binary-manager.sh stop      # Arrête
./scripts/fake-gcs-binary-manager.sh status    # Statut
./scripts/fake-gcs-binary-manager.sh logs      # Logs

# Tests
./scripts/fake-gcs-binary-manager.sh test      # Configuration test
python ./scripts/quick-test-binary.py          # Test rapide
pytest tests/integration/test_gcs_loader_binary.py -v  # Tests complets

# Maintenance
./scripts/fake-gcs-binary-manager.sh clean     # Nettoie les données
./scripts/fake-gcs-binary-manager.sh uninstall # Désinstalle
```

## 🧪 Types de tests disponibles

### 1. Test rapide (30 secondes)
```bash
python ./scripts/quick-test-binary.py
```
- Vérifie la connectivité
- Teste le client GCS
- Teste le GcsLoader basique

### 2. Tests d'intégration complets
```bash
pytest tests/integration/test_gcs_loader_binary.py -v
```
- Tests avec exports Confluence simulés
- Tests de performance
- Tests de gestion d'erreurs

### 3. Tests spécifiques
```bash
# Test de workflow complet
pytest tests/integration/test_gcs_loader_binary.py::TestCompleteWorkflow -v

# Test de performance
pytest tests/integration/test_gcs_loader_binary.py::TestGcsLoaderWithBinary::test_performance_with_binary -v
```

## 🔧 Configuration

### Variables d'environnement
```bash
export STORAGE_EMULATOR_HOST=localhost:4443
export GOOGLE_CLOUD_PROJECT=test-project
export USE_MOCKS=false  # Désactive les mocks internes
```

### Ports personnalisés
```bash
# Port 8080
./scripts/fake-gcs-binary-manager.sh start 8080
export STORAGE_EMULATOR_HOST=localhost:8080
```

### HTTPS (si nécessaire)
```bash
./scripts/fake-gcs-binary-manager.sh start 4443 https
```

## 🐛 Résolution de problèmes

### Le binaire n'est pas trouvé
```bash
./scripts/fake-gcs-binary-manager.sh install
```

### Le serveur ne démarre pas
```bash
# Vérifier si le port est libre
lsof -i :4443

# Utiliser un autre port
./scripts/fake-gcs-binary-manager.sh start 8080
```

### Les tests échouent
```bash
# Vérifier que le serveur fonctionne
curl http://localhost:4443/storage/v1/b

# Vérifier les variables d'environnement
echo $STORAGE_EMULATOR_HOST

# Voir les logs
./scripts/fake-gcs-binary-manager.sh logs
```

### Dépendances Python manquantes
```bash
pip install google-cloud-storage pytest requests
# Optionnel: pip install gcsfs
```

## 📊 Utilisation pour les tests Confluence

Cette solution est particulièrement adaptée pour tester le module Confluence loader car :

1. **Simulation réaliste** : fake-gcs-server émule parfaitement l'API GCS
2. **Données contrôlées** : Vous créez exactement les données que vous voulez tester
3. **Isolation** : Chaque test peut avoir ses propres données
4. **Performance** : Tests rapides et reproductibles

### Exemple pour Confluence
```python
from kbotloadscheduler.loader.gcs.gcs_loader import GcsLoader
from kbotloadscheduler.bean.beans import SourceBean

# Configuration pour un export Confluence
source = SourceBean(
    code="testconfluence27284",
    domain_code="EBotman",
    conf={
        'bucket': 'confluence-exports-vodcastv',
        'prefix': 'space-VODCASTV/export-2024/',
        'space_key': 'VODCASTV'
    }
)

loader = GcsLoader()
documents = loader.get_document_list(source)
# ... vos tests ici ...
```

## 🎉 Conclusion

Cette solution binaire est **recommandée** car elle est :
- Plus simple à installer et utiliser
- Plus rapide à démarrer
- Plus légère en ressources
- Compatible nativement avec toutes les plateformes
- Sans dépendance Docker

**Démarrage en 3 commandes :**
```bash
./scripts/fake-gcs-binary-manager.sh install
./scripts/fake-gcs-binary-manager.sh start
python ./scripts/quick-test-binary.py
```
