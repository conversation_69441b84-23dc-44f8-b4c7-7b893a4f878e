### Updated `CONFIGURATION_GUIDE.md`

# Module Confluence - Guide de Configuration Complet

Ce document fournit une documentation exhaustive pour toutes les options de configuration du module Confluence, en détaillant les paramètres, les valeurs par défaut, et des exemples d'utilisation, avec un accent particulier sur l'optimisation pour les systèmes RAG (Retrieval-Augmented Generation).

## Table des Matières

1.  [Philosophie de Configuration pour RAG](#1-philosophie-de-configuration-pour-rag)
2.  [Schéma de Configuration Complet (Exemple)](#2-schéma-de-configuration-complet-exemple)
3.  [Détail des Sections de Configuration](#3-détail-des-sections-de-configuration)
    -   [3.1. `basic`](#31-basic--configuration-de-base)
    -   [3.2. `filtering`](#32-filtering--options-de-filtrage)
    -   [3.3. `attachments`](#33-attachments--traitement-des-pièces-jointes)
    -   [3.4. `performance`](#34-performance--optimisation-des-performances)
    -   [3.5. `auth`](#35-auth--configuration-de-lauthentification)
    -   [3.6. `file_processing`](#36-file_processing--options-de-traitement-des-fichiers)
4.  [Recettes de Configuration](#4-recettes-de-configuration)
    -   [4.1. Production RAG (Recommandé)](#41-production-rag-recommandé)
    -   [4.2. Développement Rapide](#42-développement-rapide)
    -   [4.3. Archivage Complet (Mode Rapide)](#43-archivage-complet-mode-rapide)

---

## 1. Philosophie de Configuration pour RAG

Pour un système RAG, la **qualité et la pertinence** du contenu sont primordiales. Un contenu bruyant (pièces jointes obsolètes, pages de brouillon) dégrade la qualité des réponses. Par conséquent, les valeurs par défaut de ce chargeur sont optimisées pour la **précision** plutôt que pour la vitesse brute.

---

## 2. Schéma de Configuration Complet (Exemple)

Voici un exemple de configuration complète. Seules les sections que vous souhaitez modifier par rapport aux valeurs par défaut doivent être spécifiées.

```json
{
  "basic": {
    "spaces": ["TECH", "KBASE"],
    "max_results": 1000,
    "export_format": "markdown",
    "include_child_pages": true,
    "child_page_depth": 10
  },
  "filtering": {
    "labels": ["public", "documentation"],
    "exclude_labels": ["brouillon", "archive"],
    "last_modified_days": 90
  },
  "attachments": {
    "include_attachments": true,
    "only_referenced_attachments": true,
    "file_extensions": ["pdf", "docx", "pptx", "png", "jpg", "drawio"],
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": false
  },
  "performance": {
    "max_parallel_workers": 4,
    "retry_attempts": 3,
    "fail_fast": false
  },
  "auth": {
    "confluence_auth_mode": "global"
  },
  "file_processing": {
    "duplicate_filename_strategy": "append_id"
  }
}
```

---

## 3. Détail des Sections de Configuration

### 3.1. `basic` - Configuration de Base

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| **`spaces`** | **(Obligatoire)** Liste des clés d'espaces Confluence à traiter. | `list[str]` | `[]` |
| `max_results` | Nombre maximum de pages à récupérer par espace. `null` pour illimité. | `int \| null` | `null` |
| `export_format` | Format d'exportation pour les pages. `'markdown'` est recommandé pour RAG. | `'html' \| 'pdf' \| 'markdown'` | `'pdf'` |
| `include_child_pages`| Inclure récursivement les pages enfants. | `bool` | `true` |
| `child_page_depth` | Profondeur maximale de la récursion pour les pages enfants. | `int` | `15` |

### 3.2. `filtering` - Options de Filtrage

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| `labels` | Ne traite que les pages ayant au moins l'une de ces étiquettes. | `list[str]` | `null` |
| `exclude_labels`| Exclut toutes les pages ayant au moins l'une de ces étiquettes. | `list[str]` | `null` |
| `last_modified_days`| Ne traite que les pages modifiées au cours des N derniers jours. | `int` | `null` |
| `custom_cql` | **(Avancé)** Remplace tous les autres filtres par une requête CQL personnalisée. | `str` | `null` |

**Exemple de filtrage pour RAG :**
```json
"filtering": {
  "labels": ["documentation-finale", "validé-produit"],
  "exclude_labels": ["obsolète", "brouillon", "archive"]
}
```

### 3.3. `attachments` - Traitement des Pièces Jointes

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| `include_attachments` | Activer ou désactiver le traitement des pièces jointes. | `bool` | `false` |
| **`only_referenced_attachments`** | Mode de découverte des pièces jointes. **C'est le paramètre le plus important.** | `bool` | `true` |
| `file_extensions` | Liste des extensions de fichiers à inclure. | `list[str]` | `["pptx", "png"]`|
| `extract_drawio_as_documents` | Traite les diagrammes Draw.io comme des documents Markdown distincts. | `bool` | `false` |
| `include_drawio_png_exports` | Inclut également l'export PNG des diagrammes Draw.io. | `bool` | `false` |

#### Stratégie de Découverte des Pièces Jointes (`only_referenced_attachments`)

-   **`true` (Mode Précis - Par Défaut pour RAG)**
    -   **Comportement** : Pour chaque page, le chargeur analyse son contenu et ne télécharge **que les pièces jointes qui y sont référencées** (liées ou intégrées).
    -   **Avantage** : **Qualité maximale pour RAG**. Évite de polluer la base de connaissances avec des fichiers non pertinents, obsolètes ou dupliqués.
    -   **Inconvénient** : **Plus lent**, car cela nécessite un appel API supplémentaire par page pour analyser son contenu.

-   **`false` (Mode Rapide - Pour archivage)**
    -   **Comportement** : Le chargeur récupère en masse **toutes les pièces jointes** de toutes les pages et les filtre uniquement par extension.
    -   **Avantage** : **Très rapide**. Idéal pour une sauvegarde complète ou lorsque la vitesse prime sur la précision.
    -   **Inconvénient** : **Moins précis**. Peut inclure des fichiers non pertinents pour le contexte de la page.

### 3.4. `performance` - Optimisation des Performances

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| `max_parallel_workers` | Nombre de threads pour les appels API parallèles (ex: téléchargement de pièces jointes). | `int` | `20` |
| `retry_attempts` | Nombre de tentatives en cas d'échec d'un appel API. | `int` | `3` |
| `retry_delay_seconds` | Délai en secondes entre deux tentatives. | `int` | `2` |
| `circuit_breaker_threshold` | Nombre d'échecs consécutifs avant d'ouvrir le disjoncteur. | `int` | `5` |
| `circuit_breaker_timeout_seconds` | Durée pendant laquelle le disjoncteur reste ouvert. | `int` | `60` |
| `fail_fast` | Si `true`, le processus s'arrête à la première erreur majeure. Sinon, il logue une alerte et continue. | `bool` | `false` |

### 3.5. `auth` - Configuration de l'Authentification

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| `confluence_auth_mode` | Stratégie pour trouver les secrets d'authentification. | `'global' \| 'perimeter'` | `'global'` |

### 3.6. `file_processing` - Options de Traitement des Fichiers

| Paramètre | Description | Type | Défaut |
| :--- | :--- | :--- | :--- |
| `duplicate_filename_strategy` | Comment renommer les fichiers ayant le même nom. `'append_id'` est le plus sûr. | `'append_id' \| 'overwrite' \| ...` | `'append_id'` |
| `max_filename_length` | Longueur maximale autorisée pour un nom de fichier. | `int` | `255` |
| `default_timeout` | Timeout en secondes pour les appels API. | `int` | `30` |

---

## 4. Recettes de Configuration

### 4.1. Production RAG (Recommandé)

Cette configuration privilégie la qualité et la pertinence du contenu.

```json
{
  "basic": {
    "spaces": ["KBASE", "DOCS"],
    "export_format": "markdown",
    "child_page_depth": 10
  },
  "filtering": {
    "labels": ["public", "final"],
    "exclude_labels": ["brouillon", "archive", "wip"]
  },
  "attachments": {
    "include_attachments": true,
    "only_referenced_attachments": true,
    "file_extensions": ["pdf", "png", "jpg", "drawio"],
    "extract_drawio_as_documents": true
  },
  "performance": {
    "max_parallel_workers": 5,
    "fail_fast": false
  }
}
```

### 4.2. Développement Rapide

Idéal pour des tests rapides sur un sous-ensemble de données.

```json
{
  "basic": {
    "spaces": ["DEV-TEST"],
    "max_results": 50
  },
  "attachments": {
    "include_attachments": true,
    "only_referenced_attachments": false, // Mode rapide pour les tests
    "file_extensions": ["png"]
  },
  "performance": {
    "fail_fast": true
  }
}
```

### 4.3. Archivage Complet (Mode Rapide)

Cette configuration est conçue pour sauvegarder tout le contenu d'un espace aussi vite que possible.

```json
{
  "basic": {
    "spaces": ["ARCHIVE_ESPACE"],
    "max_results": null,
    "export_format": "pdf"
  },
  "filtering": {
    "exclude_labels": ["brouillon"]
  },
  "attachments": {
    "include_attachments": true,
    "only_referenced_attachments": false, // Mode rapide essentiel ici
    "file_extensions": ["pdf", "docx", "xlsx", "pptx", "png", "jpg"]
  },
  "performance": {
    "max_parallel_workers": 10
  }
}
```
