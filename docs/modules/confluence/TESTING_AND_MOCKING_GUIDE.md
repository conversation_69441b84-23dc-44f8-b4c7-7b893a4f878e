# Guide des Tests et du Mocking

## Aperçu

Ce guide décrit les stratégies de test et de mocking utilisées pour le module Confluence. Il couvre la configuration de l'environnement de test, la création de mocks et l'exécution de tests unitaires et d'intégration.

## Configuration de l'Environnement de Test

### Activation du Mode Mock

Pour activer le mode mock pour le développement et les tests, définissez les variables d'environnement suivantes :

```bash
# Méthode 1 : Variable d'environnement
export MOCKING_ENABLED=true

# Méthode 2 : Désactiver l'authentification externe
export SKIP_EXTERNAL_AUTH=true
```

Lorsque le mode mock est activé, le module Confluence n'accède pas à l'API Confluence réelle. Au lieu de cela, il utilise des données mockées pour simuler le comportement de l'API.

### Données Mock

Les données mock sont stockées dans le répertoire suivant :

```
src/kbotloadscheduler/mock/confluence/mock_data/
├── cql_search_space_*.json
├── attachments_for_page_*.json
└── page_content_*.json
```

#### Structure des Données Mock

- `cql_search_space_*.json` : Contient les résultats des recherches CQL pour chaque espace.
- `attachments_for_page_*.json` : Contient les listes de pièces jointes pour chaque page.
- `page_content_*.json` : Contient le contenu des pages.

## Création de Mocks

### Utilisation de create_mock_loader

La fonction `create_mock_loader` est utilisée pour créer une instance mock du chargeur Confluence.

#### Exemple de Code

```python
from kbotloadscheduler.mock.confluence import create_mock_loader

# Créer un chargeur mock
mock_loader = create_mock_loader()
```

### Configuration des Mocks

Les mocks peuvent être configurés pour simuler différents scénarios de test.

#### Exemple de Code

```python
from kbotloadscheduler.mock.confluence import create_mock_loader
from kbotloadscheduler.mock.confluence.mock_confluence_client import MockConfluenceClient

# Créer un client mock
mock_client = MockConfluenceClient()

# Configurer le comportement du client mock
mock_client.set_page_content("123456", "Contenu de la page mock")

# Créer un chargeur mock avec le client mock
mock_loader = create_mock_loader(client=mock_client)
```

## Tests Unitaires

### Structure des Tests Unitaires

Les tests unitaires sont utilisés pour tester les composants individuels du module Confluence.

#### Exemple de Code

```python
import pytest
from kbotloadscheduler.mock.confluence import create_mock_loader
from kbotloadscheduler.bean.beans import SourceBean


@pytest.fixture
def mock_loader():
    return create_mock_loader()


def test_page_discovery(mock_loader):
    # Créer une source de test
    source = SourceBean(
        src_type="",
        configuration='{"spaces":["TECH"],"export_format":"markdown"}'
    )

    # Récupérer la liste des documents
    documents = mock_loader.get_document_list(source)

    # Vérifier les résultats
    assert isinstance(documents, list)
    assert len(documents) > 0
    assert all(isinstance(doc, DocumentBean) for doc in documents)
```

### Exécution des Tests Unitaires

Les tests unitaires peuvent être exécutés à l'aide de pytest.

#### Exemple de Commande

```bash
pytest tests/loader/confluence/
```

## Tests d'Intégration

### Structure des Tests d'Intégration

Les tests d'intégration sont utilisés pour tester l'interaction entre les différents composants du module Confluence.

#### Exemple de Code

```python
import pytest
from kbotloadscheduler.loader.confluence import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

def test_real_confluence():
    # Créer une source de test réelle
    source = SourceBean(
        src_type="confluence",
        configuration='{"spaces":["TECH"],"export_format":"markdown"}'
    )

    # Créer un chargeur Confluence réel
    loader = ConfluenceLoader(ConfigWithSecret())

    # Récupérer la liste des documents
    documents = loader.get_document_list(source)

    # Vérifier les résultats
    assert isinstance(documents, list)
    assert len(documents) > 0
    assert all(isinstance(doc, DocumentBean) for doc in documents)
```

### Exécution des Tests d'Intégration

Les tests d'intégration peuvent être exécutés à l'aide de pytest.

#### Exemple de Commande

```bash
pytest tests/integration/confluence/
```

## Bonnes Pratiques

### Utilisation de Mocks

Utilisez des mocks pour isoler les composants et simuler différents scénarios de test.

### Configuration des Tests

Configurez les tests pour utiliser des données mock ou des instances Confluence réelles en fonction des besoins.

### Exécution Régulière des Tests

Exécutez les tests unitaires et d'intégration régulièrement pour garantir la qualité du code.

## Dépannage

### Problème : Tests Échouent en Mode Mock

**Symptômes** :
- Les tests échouent en mode mock
- Les données mock ne sont pas chargées correctement

**Solutions** :
- Vérifiez que la variable d'environnement `MOCKING_ENABLED` est définie sur `true`.
- Vérifiez que les données mock sont stockées dans le répertoire correct.
- Vérifiez que les données mock sont formatées correctement.

### Problème : Tests Échouent avec une Instance Confluence Réelle

**Symptômes** :
- Les tests échouent avec une instance Confluence réelle
- Les données ne sont pas récupérées correctement

**Solutions** :
- Vérifiez que les informations d'identification Confluence sont correctes.
- Vérifiez que l'instance Confluence est accessible.
- Vérifiez que les autorisations sont correctement configurées.

Ce guide fournit des informations complètes sur les stratégies de test et de mocking utilisées pour le module Confluence. Utilisez-le comme référence pour configurer l'environnement de test, créer des mocks et exécuter des tests unitaires et d'intégration.
