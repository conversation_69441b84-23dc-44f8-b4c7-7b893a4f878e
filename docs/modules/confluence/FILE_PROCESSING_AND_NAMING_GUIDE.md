# Guide de Traitement et de Nommage des Fichiers - Complet

## Aperçu

Ce guide détaille les stratégies de traitement et de nommage des fichiers utilisées par le module Confluence. Il couvre la génération de noms de fichiers sécurisés, la gestion des doublons et la structure des chemins de destination.

## Stratégie de Nommage des Fichiers

### Génération de Noms de Fichiers Sécurisés

Le système garantit des noms de fichiers sécurisés et uniques grâce aux règles suivantes :

1. **Remplacement des caractères** : Remplace les caractères dangereux par des underscores.
2. **Limites de longueur** : Applique une longueur maximale pour les noms de fichiers (par défaut : 255).
3. **Résolution des collisions** : Ajoute un suffixe unique pour les doublons.
4. **Préservation de l'extension** : Maintient les extensions de fichiers d'origine.

### Composants des Noms de Fichiers

**Format** : `{nom_original}_{id_confluence}.{extension}`

Exemples :
- Page : `guide_de_demarrage_123456.md`
- Pièce jointe : `diagramme_architecture.pdf_att789012`
- Draw.io : `flux_de_processus.drawio_123456_macro_0`

### Exemples de Code

#### Génération d'un Nom de Fichier Sécurisé

```python
from kbotloadscheduler.loader.confluence.utils.file_utils import create_safe_filename

safe_name = create_safe_filename("Guide de l'utilisateur v2.0.pdf")
# Résultat : "guide_de_l_utilisateur_v2.0.pdf"
```

#### Création d'un Nom de Fichier Unique

```python
from kbotloadscheduler.loader.confluence.utils.file_utils import create_unique_filename

unique_name = create_unique_filename("document.pdf", "/chemin/de/sortie")
# Résultat : "document_1.pdf" si une collision existe
```

## Structure des Chemins de Destination

```
chemin_de_sortie/
├── {code_domaine}/
│   └── {code_source}/
│       ├── pages/
│       │   ├── {nom_page}_{id_page}.md
│       │   └── {nom_page}_{id_page}/
│       │       └── {nom_piece_jointe}_{id_piece_jointe}.{ext}
│       ├── pieces_jointes/
│       │   └── {nom_piece_jointe}_{id_piece_jointe}.{ext}
│       └── diagrammes/
│           └── {nom_diagramme}_{id_diagramme}.md
```

## Configuration

### Options de Traitement des Fichiers

```json
{
  "file_processing": {
    "duplicate_filename_strategy": "append_id",
    "temp_extensions": [".tmp", ".temp", ".bak", ".backup", ".swp"],
    "max_filename_length": 255,
    "default_timeout": 30
  }
}
```

### Stratégie des Noms de Fichiers Dupliqués

#### append_id (Par Défaut)

```json
{"file_processing": {"duplicate_filename_strategy": "append_id"}}
```

**Exemple** : `document.pdf` → `document_123456.pdf`

#### append_counter

```json
{"file_processing": {"duplicate_filename_strategy": "append_counter"}}
```

**Exemple** : `document.pdf` → `document_1.pdf`, `document_2.pdf`

#### overwrite

```json
{"file_processing": {"duplicate_filename_strategy": "overwrite"}}
```

**Attention** : Peut entraîner une perte de contenu.

### Gestion de la Longueur des Noms de Fichiers

```json
// Environnements stricts (anciens systèmes de fichiers)
{"file_processing": {"max_filename_length": 100}}

// Par défaut (systèmes de fichiers modernes)
{"file_processing": {"max_filename_length": 255}}

// Très permissif
{"file_processing": {"max_filename_length": 500}}
```

## Bonnes Pratiques

### Assurer des Noms de Fichiers Uniques

Utilisez la stratégie `append_id` pour éviter les collisions de noms de fichiers et la perte de données.

### Gérer les Fichiers Temporaires

Configurez la liste `temp_extensions` pour ignorer les fichiers temporaires et éviter de les traiter.

### Limiter la Longueur des Noms de Fichiers

Ajustez `max_filename_length` en fonction des limitations de votre système de fichiers cible.

## Dépannage

### Problème : Noms de Fichiers Trop Longs

**Symptômes** :
- Erreurs de création de fichiers
- Troncature des noms de fichiers

**Solutions** :
- Réduisez la valeur de `max_filename_length`.
- Nettoyez les noms de fichiers d'origine pour supprimer les caractères inutiles.

### Problème : Collisions de Noms de Fichiers

**Symptômes** :
- Perte de données
- Fichiers écrasés

**Solutions** :
- Utilisez la stratégie `append_id` ou `append_counter`.
- Vérifiez la logique de génération des noms de fichiers.

### Problème : Fichiers Temporaires Inclus

**Symptômes** :
- Fichiers temporaires traités
- Données incomplètes

**Solutions** :
- Ajoutez les extensions de fichiers temporaires à la liste `temp_extensions`.