# Traitement Draw.io - Guide technique complet

## Aperçu

Le module Confluence dispose d'un traitement sophistiqué des diagrammes Draw.io qui extrait les informations sémantiques des diagrammes et les convertit en contenu structuré et indexable. Ce guide couvre la chaîne de traitement complète, de la détection au traitement final.

## Architecture

### Flux de la chaîne de traitement

```mermaid
graph TD
    A[Page Confluence] --> B[Détecteur Draw.io]
    B --> C[Identification des diagrammes]
    C --> D[Extracteur Draw.io]
    D --> E[Analyse XML]
    E --> F[Extraction Formes/Connexions]
    F --> G[Processeur Draw.io]
    G --> H[Génération Markdown]
    H --> I[Document Structuré]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#bbf,stroke:#333,stroke-width:2px
```

## Stratégie de détection

### Méthodes de détection

Les diagrammes Draw.io sont détectés via plusieurs stratégies :

#### 1. Détection par pièce jointe
**Emplacement** : [`drawio/detector.py`](src/kbotloadscheduler/loader/confluence/drawio/detector.py:35)

```python
# Correspondance de motifs pour les extensions de fichier Draw.io
DRAWIO_EXTENSIONS = {'.drawio', '.drawio.xml', '.dio'}

# Méthode de détection
def find_from_attachment_meta(attachments: List[Dict]) -> List[DrawioDiagram]:
    """
    Identifier les diagrammes Draw.io à partir des métadonnées des pièces jointes.

    Motifs de reconnaissance :
    - le nom de fichier se termine par .drawio ou .drawio.xml
    - mediaType = "application/octet-stream"
    - motifs de contenu spécifiques dans le fichier
    """
```

#### 2. Détection de diagramme intégré
**Emplacement** : [`drawio/detector.py`](src/kbotloadscheduler/loader/confluence/drawio/detector.py:78)

```python
# Détection de macro dans le contenu Confluence
DRAWIO_MACRO_PATTERN = r'ac:name="drawio".*?\[target=([^\]]+).*?\]'

# Motifs de détection
- macros ac:name="drawio"
- macros ac:name="draw-io"
- pièces jointes de diagrammes drawio intégrées
```

#### 3. Détection basée sur l'image
```python
# Identifier les PNG qui contiennent des métadonnées Draw.io intégrées
def contains_drawio_metadata(image_data: bytes) -> bool:
    """
    Vérifier si un PNG contient du XML Draw.io intégré.
    """
    return b'<mxfile ' in image_data
```

## Extraction et analyse XML

### Analyse de la structure XML

**Emplacement** : [`drawio/extractor.py`](src/kbotloadscheduler/loader/confluence/drawio/extractor.py:1)

#### Schéma XML de base
```xml
<mxfile>
    <diagram id="diagram-1">
        <mxGraphModel>
            <root>
                <mxCell id="0" />      <!-- Conteneur racine -->
                <mxCell id="1" parent="0" />    <!-- Conteneur de page -->
                <mxCell id="2" value="Début" ... />    <!-- Forme -->
                <mxCell id="3" ... />    <!-- Forme -->
                <mxCell id="4" edgeEquation="..." ... /> <!-- Connexion -->
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
```

#### Informations extraites

| Composant | Exemple | Méthode d'extraction |
|-----------|---------|------------------|
| **Formes** | Début, Processus, Décision | Éléments `<mxCell>` avec des formes |
| **Connexions** | A→B→C | Éléments `<mxCell>` de type arête avec équation |
| **Texte** | Étiquettes, titres, descriptions | Attribut `value` |
| **Styles** | Couleurs, tailles, positions | Analyse complexe de l'attribut `style` |
| **Connexions** | Flèches de flux | Attributs `source`, `target` |

### Structures de données

#### Métadonnées de forme
```python
@dataclass
class DrawioShape:
    id: str                    # Identifiant unique
    shape_type: str           # "start", "process", "decision", etc.
    text: str                 # Texte affiché
    style: Dict[str, str]     # Attributs de style analysés
    geometry: Dict            # x, y, largeur, hauteur
    parent: Optional[str]     # Conteneur/groupe parent
```

#### Métadonnées de connexion
```python
@dataclass
class DrawioConnection:
    id: str                    # Identifiant de la connexion
    source: Optional[str]     # ID de la forme source
    target: Optional[str]     # ID de la forme cible
    text: str                 # Étiquette de la connexion
    style: str                # Attributs de style de la connexion
```

## Conversion en Markdown

### Structure générée

#### Format de sortie standard
```markdown
# Diagramme de flux de processus - Inscription de l'utilisateur

## Aperçu
Flux du processus d'inscription de l'utilisateur, de l'inscription initiale à la vérification par e-mail.

## Résumé des formes
- **Début** (Ovale) : "Commencer l'inscription"
- **Processus** (Rectangle) : "Valider l'e-mail"
- **Décision** (Losange) : "Format valide ?"
- **Processus** (Rectangle) : "Créer le compte"
- **Fin** (Cercle) : "Inscription terminée"

## Connexions
1. [Début → Valider l'e-mail] "L'utilisateur soumet le formulaire"
2. [Valider l'e-mail → Format valide ?] "Vérifier le format"
3. [Format valide ? → Créer le compte] "Oui"
4. [Format valide ? → Fin] "Non" (Chemin d'erreur)
5. [Créer le compte → Inscription terminée] "Succès"

## Structure complète
### Étape 1 : Interface utilisateur
- **Début** (Position : x=100, y=50)
- **Formulaire d'entrée** (Rectangle, contient : champs e-mail, mot de passe)

### Étape 2 : Validation
- **Validateur d'e-mail** (Rectangle de processus)
  - Valide le format de l'e-mail
  - Vérifie la disponibilité du domaine
  - Texte : "<EMAIL>"

### Étape 3 : Opérations sur la base de données
- **Créer utilisateur** (Rectangle de processus)
  - Insère l'enregistrement de l'utilisateur
  - Génère un jeton d'activation
  - Envoie un e-mail de vérification
```

### Traitement avancé

#### Prise en charge des couloirs (Swimlanes)
```markdown
## Disposition en couloirs
### Couloir Utilisateur
- [Début] -> [Remplir le formulaire] -> [Soumettre]

### Couloir Système
- [Valider] -> [Créer le compte] -> [Envoyer l'e-mail]

### Couloir Admin
- [Examiner] -> [Approuver/Rejeter]
```

#### Navigation dans les sous-processus
```markdown
## Processus associés
- **Flux d'activation de compte** (Lien vers : 12345_macro_1)
- **Processus de réinitialisation de mot de passe** (Lien vers : 12345_macro_2)
- **Flux de mise à jour du profil** (Lien vers : 12345_macro_3)
```

## Configuration pour le traitement Draw.io

### Activer le traitement
```json
{
  "attachments": {
    "include_attachments": true,
    "extract_drawio_as_documents": true,
    "file_extensions": ["drawio", "svg", "png"]
  }
}
```

### Configuration avancée
```json
{
  "attachments": {
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": false,
    "file_extensions": ["drawio", "daio", "drawio.png"]
  }
}
```

## Exemples d'utilisation de l'API

### Extraire Draw.io d'une seule page
```python
from kbotloadscheduler.loader.confluence.drawio.detector import DrawioDetector
from kbotloadscheduler.loader.confluence.drawio.extractor import DrawioExtractor

# Initialiser les composants
detector = DrawioDetector()
extractor = DrawioExtractor()

# Traiter le contenu de la page
page_data = client.get_page_content(page_id)
diagrams = detector.find_from_page_content(page_data)

for diagram in diagrams:
    metadata = extractor.extract_diagram_info(diagram.xml_content)
    markdown = extractor.convert_to_markdown(metadata)
```

### Approche de traitement par lots
```python
def process_space_drawio_diagrams(space_key: str) -> List[Dict]:
    """Traiter tous les diagrammes Draw.io d'un espace."""
    diagrams = []

    # Trouver toutes les pages avec des diagrammes Draw.io
    pages = search.find_pages_with_attachments(space_key)

    for page in pages:
        attachments = client.get_page_attachments(page['id'])

        drawio_attachments = [
            att for att in attachments
            if detector.is_drawio_attachment(att)
        ]

        for attachment in drawio_attachments:
            diagram = process_drawio_attachment(attachment)
            diagrams.append(diagram)

    return diagrams
```

## Fonctionnalités avancées

### Traitement hiérarchique
Prise en charge des diagrammes avec des structures complexes :
- **Conteneurs** : Groupes, piscines, couloirs
- **Sous-processus** : Composants de diagramme réutilisables
- **Références croisées** : Liens entre les diagrammes

### Analyse sémantique
```python
# Extraire les règles métier des losanges de décision
business_rules = extractor.extract_business_rules(xml_content)
# Retourne : [{"condition": "Montant > 1000$", "action": "Approbation requise"}]

# Extraire les flux de données
data_flows = extractor.extract_data_flows(xml_content)
# Retourne : [{"source": "Base de données", "target": "API", "data_type": "JSON"}]
```

## Exemples de configuration

### Environnement de développement
```json
{
  "attachments": {
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": false,
    "file_extensions": ["drawio"]
  },
  "basic": {
    "child_page_depth": 3,
    "max_results": 20
  }
}
```

### Environnement de production
```json
{
  "attachments": {
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": true,
    "file_extensions": ["drawio", "drawio.png", "drawio.svg"]
  },
  "performance": {
    "use_memory_efficient_processing": true,
    "max_parallel_workers": 2
  }
}
```

### Configuration d'archivage
```json
{
  "attachments": {
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": true,
    "attachment_filter_mode": "content_used"
  },
  "basic": {
    "export_format": "markdown",
    "child_page_depth": 10
  }
}
```

## Gestion des erreurs

### Problèmes courants et solutions

#### Problème : Liste de diagrammes vide
**Cause** : Aucun diagramme Draw.io dans l'espace ou contenu malformé
**Solution** :
```python
- Vérifier que l'espace contient des pièces jointes .drawio
- Vérifier que les macros Draw.io utilisent des formats standard
- Vérifier les configurations de macros personnalisées
```

#### Problème : Erreur d'analyse XML
**Cause** : XML du diagramme corrompu
**Solution** :
```python
- Activer la journalisation pour la sortie de débogage
- Vérifier la validité du XML du diagramme
- Gérer le contenu partiel avec souplesse
```

#### Problème : Connexions manquantes
**Cause** : Équations de connexion complexes ou style personnalisé
**Solution** :
```python
- Activer l'analyse avancée des connecteurs
- Utiliser l'inspection visuelle pour les diagrammes complexes
- Configurer des méthodes de détection de secours
```

#### Problème : Problèmes de performance
**Cause** : Diagrammes volumineux ou volumes élevés
**Solution** :
```json
{
  "performance": {
    "use_memory_efficient_processing": true,
    "retry_attempts": 3,
    "cache_ttl_minutes": 30
  }
}
```

## Exemples d'intégration

### Pipeline d'extraction vers AWS S3
```python
def process_confluence_space_to_s3(space_key: str, bucket: str):
    """Traiter l'espace et téléverser sur S3, y compris les diagrammes Draw.io."""

    # Initialiser le client AWS S3
    s3_client = boto3.client('s3')

    # Traiter chaque diagramme
    diagrams = extract_drawio_diagrams(space_key)

    for diagram in diagrams:
        # Convertir en markdown
        markdown = diagram.to_markdown()

        # Téléverser sur S3
        key = f"drawio-processing/{space_key}/{diagram.id}.md"
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=markdown,
            ContentType='text/markdown'
        )

        # Téléverser également les fichiers originaux
        original_key = f"drawio-attachments/{space_key}/{diagram.filename}"
        s3_client.put_object(
            Bucket=bucket,
            Key=original_key,
            Body=diagram.xml_content,
            ContentType='application/xml'
        )
```

### Stockage en base de données avec relations
```python
def store_diagram_metadata(diagram: DrawioDiagram):
    """Stocker les informations du diagramme avec le mappage des relations."""

    # Stocker les métadonnées du diagramme
    diagram_record = {
        'id': diagram.id,
        'space_key': diagram.space_key,
        'page_id': diagram.page_id,
        'title': diagram.title,
        'shape_count': len(diagram.shapes),
        'connection_count': len(diagram.connections),
        'created_at': datetime.utcnow()
    }

    diagram_table.insert(diagram_record)

    # Stocker les relations entre les formes
    for shape in diagram.shapes:
        shape_record = {
            'diagram_id': diagram.id,
            'shape_id': shape.id,
            'type': shape.shape_type,
            'text': shape.text,
            'connections_in': [],
            'connections_out': []
        }
        shapes_table.insert(shape_record)
```

## Considérations sur la performance

### Optimisation de la mémoire
```json
{
  "performance": {
    "use_memory_efficient_processing": true,
    "max_parallel_workers": 2
  }
}
```
