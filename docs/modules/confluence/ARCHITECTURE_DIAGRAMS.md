# Diagrammes d'Architecture du Module Confluence

## Aperçu

Ce document contient les diagrammes d'architecture du module Confluence. Ces diagrammes fournissent une vue d'ensemble visuelle de la structure, des composants et des interactions du module.

## Diagramme d'Architecture de Haut Niveau

```mermaid
graph TB
    CL[ConfluenceLoader] -->|Orchestre| SP[SpaceProcessor]
    CL -->|Télécharge| CD[ContentDownloader]
    CL -->|Configure| CF[ConfluenceConfig]

    SP -->|Recherche| CS[CqlSearch]
    SP -->|Identifie| DD[DrawioDetector]

    CS -->|Requêtes| CC[ConfluenceClient]
    CD -->|Récupère| CC

    DD -->|Traite| DP[DrawioProcessor]
    DP -->|Convertit| MC[MarkdownConverter]

    CC -->|Authentifie| CCR[ConfluenceCredentials]

    style CL fill:#f9f,stroke:#333,stroke-width:2px
    style CC fill:#bbf,stroke:#333,stroke-width:2px
```

### Description

Ce diagramme montre les principaux composants du module Confluence et leurs interactions.

- **ConfluenceLoader** : Orchestre les opérations de chargement des données.
- **SpaceProcessor** : Découvre le contenu dans un espace Confluence.
- **ContentDownloader** : Télécharge le contenu des documents.
- **ConfluenceConfig** : Configure le module.
- **CqlSearch** : Effectue des recherches CQL.
- **DrawioDetector** : Détecte les diagrammes Draw.io.
- **ConfluenceClient** : Client API pour Confluence.
- **DrawioProcessor** : Traite les diagrammes Draw.io.
- **MarkdownConverter** : Convertit le contenu en Markdown.
- **ConfluenceCredentials** : Gère les informations d'identification Confluence.

## Diagramme de Flux de Données

```mermaid
flowchart TD
    subgraph "Phase de Découverte"
        A[SourceBean] -->|configuration| B[ConfluenceConfig]
        B --> C[SpaceProcessor]
        C --> D[Trouver les pages racines]
        D --> E[Traiter récursivement]
        E --> F[Identifier les diagrammes Draw.io]
        E --> G[Traiter les pièces jointes]
        F & G --> H[DocumentBeans]
    end

    subgraph "Phase de Téléchargement"
        H --> I[ContentDownloader]
        I --> J[Détection du type]
        J --> K{Page / Pièce jointe / Diagramme}
        K -->|Page| L[Télécharger la page]
        K -->|Pièce jointe| M[Télécharger le fichier]
        K -->|Diagramme| N[Traiter Draw.io]
        L & M & N --> O[Enregistrer sur le disque]
        O --> P[Retourner les métadonnées]
    end
```

### Description

Ce diagramme illustre le flux de données à travers le module Confluence, de la phase de découverte à la phase de téléchargement.

## Diagramme de Traitement de la Configuration

```mermaid
graph LR
    A[JSON Brut] --> B[Séparation des Clés]
    B --> C[Transformation Héritée]
    C --> D[Validation Pydantic]
    D --> E[Conversion de Type]
    E --> F[Validation des Règles]
    F --> G[Objet ConfluenceConfig]

    style G fill:#9f9,stroke:#333,stroke-width:2px
```

### Description

Ce diagramme montre le processus de traitement de la configuration, de la réception du JSON brut à la création de l'objet `ConfluenceConfig`.

## Diagramme de Récupération des Erreurs

```mermaid
stateDiagram-v2
    [*] --> Sain: Opération Normale
    Sain --> Avertissement: 3-4 échecs
    Avertissement --> NonSain: 5 échecs
    NonSain --> Récupération: Délai de 60s
    Récupération --> Sain: Vérification du succès
    Récupération --> NonSain: Toujours en échec

    state Sain {
        [*] --> Réessayer: Échec temporaire
        Réessayer --> [*]: Succès
        Réessayer --> [*]: Nombre maximal de tentatives
    }

    state NonSain {
        [*] --> CircuitOuvert: Automatique
        CircuitOuvert --> [*]: Forcer l'échec
    }
```

### Description

Ce diagramme illustre le flux de récupération des erreurs, y compris les états sain, avertissement, non sain et récupération.

Ce document fournit une vue d'ensemble visuelle de la structure, des composants et des interactions du module Confluence. Utilisez ces diagrammes pour mieux comprendre l'architecture du module.