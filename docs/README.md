# 📚 Documentation Détaillée - kbot-load-scheduler

Bienvenue dans la section de documentation du projet `kbot-load-scheduler`. Ce répertoire contient des analyses approfondies de l'architecture, des flux de données et des guides d'utilisation des API.

Vous trouverez ici les documents suivants :

*   **[📖 Vue d'Ensemble du Système (ARCHITECTURE.md)](./ARCHITECTURE.md)**
    *Une description complète de l'architecture, des composants, des flux de travail, des patterns de conception et des stratégies de déploiement.*

*   **[📡 Référence des API (API_REFERENCE.md)](./API_REFERENCE.md)**
    *Un guide pratique et exhaustif de tous les endpoints de l'API FastAPI, avec des exemples de requêtes, des schémas de données et des instructions pour les tests locaux.*

*   **[🚀 Guide de Déploiement Confluence RAG sur Cloud Run (CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)](./CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)**
    *Un guide pas à pas pour déployer et configurer le système dans un environnement RAG sur Google Cloud Run.*

---

Pour revenir au guide de démarrage rapide et à la configuration générale du projet, consultez le [README.md principal](../README.md).