# kbot-load-scheduler

![](https://img.shields.io/badge/version-0.19.1-blue.svg)
![](https://img.shields.io/badge/python-3.12+-green.svg)
![](https://img.shields.io/badge/license-Proprietary-orange.svg)

`kbot-load-scheduler` est le moteur d'orchestration central de l'écosystème Knowledge Bot. Il est conçu pour automatiser le chargement, la synchronisation et la préparation des données provenant de multiples sources de connaissance, garantissant que le Knowledge Bot dispose en permanence d'informations fraîches, pertinentes et prêtes pour l'embedding.

Ce système a pour objectifs de :

-   **Automatiser** l'intégralité du cycle de vie de la connaissance, de la source à la base vectorielle.
-   Assurer un **chargement de données fiable et résilient**, capable de reprendre sur erreur.
-   Permettre une **intégration facile** de nouvelles sources grâce à une architecture modulaire.
-   **Optimiser les performances** de traitement pour une mise à disposition rapide des informations.
-   Fournir une **traçabilité complète** des opérations via un système de fichiers d'état.

## 📚 Documentation Approfondie

Pour une analyse complète de l'architecture, des flux de données et des décisions de conception, veuillez consulter notre **[Vue d'Ensemble du Système](./docs/ARCHITECTURE.md)**.

## ✨ Fonctionnalités Clés

-   **Orchestration avancée via GCS** : Le flux de travail est piloté par un système de fichiers d'état sur Google Cloud Storage, permettant une distribution des tâches, une reprise sur erreur et une traçabilité claire des étapes (lister, comparer, télécharger, indexer, supprimer).
-   **Architecture de `Loader` modulaire** : Système extensible basé sur une interface `AbstractLoader`, permettant d'ajouter facilement de nouvelles sources.
-   **Support de sources multiples optimisées pour RAG** :
    -   **Confluence** : Synchronisation avancée des pages, blogs et pièces jointes, avec export Markdown, filtrage intelligent par labels, et support des instances Confluence Server (on-premise).
    -   **SharePoint** : Extraction de documents (PDF, Office, etc.) et de leurs relations hiérarchiques depuis des sites et bibliothèques.
    -   **Google Cloud Storage** : Chargement direct de fichiers et documents.
    -   **Sources personnalisées ("Basic")** : Un chargeur générique pour s'intégrer à des API spécifiques.
-   **API REST complète** : Interface FastAPI pour l'intégration avec d'autres systèmes (`kbot-back`, `kbot-embedding`) et pour le déclenchement des tâches.
-   **Sécurité intégrée** : Gestion centralisée des secrets via Google Secret Manager avec un fallback sur des fichiers locaux pour le développement, et authentification aux API (PAT, OAuth, JWT).
-   **Déploiement Cloud-Native** : Conçu pour s'exécuter sur Cloud Run, avec une planification des tâches via Cloud Scheduler et des optimisations pour les environnements éphémères.

## 🏗️ Architecture et Flux de Travail

Le `kbot-load-scheduler` s'intègre au sein de la plateforme GCP et de l'écosystème Knowledge Bot pour orchestrer le flux de données depuis les sources externes jusqu'à l'API d'embedding.

![Architecture Simplifiée](./docs/assets/simplify-architecture-diagram-02-06-2025-17_48_36.png)


### Flux de Travail Simplifié

L'orchestration est un processus en plusieurs étapes, piloté par des fichiers d'état sur GCS :

1.  **Déclenchement** : Un `Cloud Scheduler` lance le processus pour un périmètre donné (ex: `production`, `mktsearch`).
2.  **Configuration des Sources** : Le service appelle `kbot-back` pour obtenir la liste des sources à traiter. Pour chaque source, un fichier `getlist.json` est créé sur GCS.
3.  **Liste des Documents (`/loader/list`)** : Pour chaque `getlist.json`, le `Loader` approprié est appelé pour lister tous les documents de la source. Le résultat est stocké dans un fichier `list.json` sur GCS.
4.  **Comparaison (`/document/compare`)** : Le fichier `list.json` est comparé à l'état connu dans la base vectorielle (via `kbot-embedding`). Le système génère alors la liste des documents à ajouter, mettre à jour ou supprimer.
5.  **Téléchargement (`/loader/document`)** : Le contenu des nouveaux documents ou des documents modifiés est téléchargé depuis la source et stocké sur GCS, avec un fichier de métadonnées (`.metadata.json`) associé.
6.  **Embedding & Suppression (`/document/embedd`, `/document/remove`)** : Les nouveaux contenus sont envoyés à l'API `kbot-embedding` pour être vectorisés et indexés, tandis que les documents obsolètes sont supprimés.

## 🚀 Installation

### Prérequis

-   Python 3.12+
-   Accès à un projet Google Cloud Platform (GCP)
-   `pipenv` pour la gestion des dépendances
-   Accès aux instances des sources de données (Confluence, SharePoint, etc.)

**Pour un déploiement RAG sur Cloud Run :** Voir le [Guide de Déploiement Confluence RAG](./docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)

### Installation Recommandée

Le projet utilise `pipenv` pour une gestion robuste des dépendances. La méthode la plus simple pour configurer votre environnement est d'utiliser la cible `init` du `Makefile`.

```bash
# Cloner le dépôt
git clone <https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler.git>
cd kbot-load-scheduler

# Initialiser l'environnement et installer toutes les dépendances
make init
```

Cette commande installe `pipenv` si nécessaire, crée un environnement virtuel avec Python 3.12, et installe toutes les dépendances requises pour la production et le développement (`requirements.txt` et `tests/test-requirements.txt`).

## 🔧 Configuration

1.  **Variables d'environnement** : Créez un fichier `.env` en vous basant sur `.env.example`. Il est utilisé pour surcharger les valeurs par défaut lors du développement local.
2.  **Secrets d'accès** : Les secrets (tokens, mots de passe) sont gérés via Google Secret Manager en production. Pour le développement local, ils sont lus depuis des fichiers locaux.
    -   Consultez `conf/etc/secrets/tests/README_SECRETS.md` pour savoir comment configurer les secrets pour les tests.
    -   Exemples de noms de secrets : `{perimeter_code}-confluence-credentials`, `sharepoint-credentials`.

## 📊 Utilisation

### Démarrer le service localement

La commande `make start` est la méthode recommandée pour lancer le service en mode développement. Elle configure toutes les variables d'environnement nécessaires et lance `uvicorn` avec rechargement automatique.

```bash
# Démarrage local (lit les variables du Makefile, surchargeables par .env)
make start
```

Le service sera accessible sur `http://localhost:8092` (ou le port défini par `LOAD_SCHEDULER_PORT`). La documentation interactive de l'API (Swagger UI) sera disponible sur `http://localhost:8092/docs`.

### Exemples d'appels API

```bash
# Lancer le chargement pour un périmètre spécifique (simule un appel de Cloud Scheduler)
curl -X POST "http://localhost:8092/schedule/treatments/production/launch"
```

*Consultez la **[Référence de l'API](./docs/API_REFERENCE.md)** ou la Swagger UI pour la liste complète des endpoints.*

## 🧪 Tests

Le projet est doté d'une suite de tests complète utilisant `pytest`, organisée via le `Makefile`.

### Exécuter les tests unitaires

```bash
# Exécute tous les tests unitaires (hors intégration) et génère le rapport de couverture
make unit-tests
```

### Exécuter les tests d'intégration

Les tests d'intégration nécessitent des dépendances externes et des credentials valides.

```bash
# Exemple pour lancer les tests d'intégration Confluence
make integration-tests-confluence
```

### Organisation des Fichiers de Tests

Grâce à la configuration dans `pytest.ini`, tous les artéfacts de test sont générés dans le répertoire `./tmp/` pour garder le projet propre :
-   **Fichiers temporaires de tests** : `./tmp/pytest/`
-   **Rapports de couverture HTML** : `./tmp/coverage/`

### Analyse de Sécurité

Le projet utilise `bandit` pour l'analyse de sécurité statique.

```bash
# Exécuter l'analyse de sécurité et générer un rapport
make bandit
```

## 🎯 Optimisations pour les Systèmes RAG (Retrieval-Augmented Generation)

`kbot-load-scheduler` est fondamentalement conçu pour fournir du contenu de haute qualité, structuré et riche en métadonnées, indispensable pour alimenter efficacement des systèmes RAG. Cette philosophie s'applique à toutes les sources de données, présentes et futures.

### Pour Confluence :
-   **Export Markdown natif** : Conversion optimale des pages Confluence pour un traitement textuel par les LLMs.
-   **Extraction de métadonnées relationnelles** : Préserve les liens parent-enfant entre les pages et les pièces jointes.
-   **Conversion des diagrammes Draw.io** : Transforme les schémas en descriptions textuelles ou en SVG, les rendant "compréhensibles" pour les modèles.

### Pour SharePoint :
-   **Extraction de Fichiers Structurés** : Gère les formats bureautiques courants (PDF, DOCX, XLSX, etc.) qui contiennent une information riche.
-   **Préservation de la Hiérarchie** : Capture la structure des dossiers et sous-dossiers pour l'injecter comme métadonnées, donnant un contexte précieux au système RAG.

### Une Architecture "RAG-Ready" :
-   **Métadonnées Standardisées** : Chaque `loader` produit un ensemble cohérent de métadonnées, ce qui rend le pipeline RAG en aval agnostique à la source.
-   **Interface Extensible** : Ajouter une nouvelle source de données (ex: Zendesk, JIRA) via l'interface `AbstractLoader` prépare automatiquement son contenu pour le RAG.
-   **🛡️ Robustesse Intégrée** : La logique de *retry* automatique garantit un flux de données fiable vers le système RAG, quelle que soit la stabilité de la source.

### Documentation Spécialisée
-   **Guide Confluence RAG** : [`src/kbotloadscheduler/loader/confluence/README.md`](./src/kbotloadscheduler/loader/confluence/README.md)
-   **Guide Déploiement Cloud Run** : [`docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md`](./docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)